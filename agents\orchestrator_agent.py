"""
Orchestrator Agent for the Multi-Agent RAG system.

This module contains the OrchestratorAgent class that acts as a central coordinator,
intelligently routing queries to appropriate specialized agents and coordinating responses.
"""

from typing import Dict, Any, List, Optional
import re
import logging

from langchain_ollama import OllamaLLM

from config import (
    AgentType, QueryIntent, ModelConfig, IntentDetectionConfig, 
    AgentCapabilities, PromptTemplates, RetryConfig, ErrorMessages,
    FallbackMessages
)
from .base_agent import BaseAgent, AgentProcessingError
from .text_agent import TextAgent
from .image_agent import ImageAgent

# Configure logging
logger = logging.getLogger(__name__)


class OrchestratorAgent(BaseAgent):
    """
    Central orchestrator agent that manages and coordinates all specialized agents.
    
    This agent provides:
    - Intelligent query analysis and intent detection
    - Smart routing to appropriate specialized agents
    - Response coordination for multi-modal queries
    - Fallback handling and error recovery
    - Unified interface for all agent capabilities
    """
    
    def __init__(self, text_agent: TextAgent, image_agent: ImageAgent):
        """
        Initialize the OrchestratorAgent with specialized agents.
        
        Args:
            text_agent: Initialized TextAgent instance
            image_agent: Initialized ImageAgent instance
        """
        super().__init__(
            agent_type=AgentType.TEXT_RAG,  # Orchestrator uses text model
            capabilities=AgentCapabilities.ORCHESTRATOR_CAPABILITIES
        )
        
        self.text_agent = text_agent
        self.image_agent = image_agent
        self.llm = None
        
        # Intent detection configuration
        self.web_search_keywords = IntentDetectionConfig.WEB_SEARCH_KEYWORDS
        self.youtube_keywords = IntentDetectionConfig.YOUTUBE_KEYWORDS
        self.knowledge_base_keywords = IntentDetectionConfig.KNOWLEDGE_BASE_KEYWORDS
        self.multi_modal_keywords = IntentDetectionConfig.MULTI_MODAL_KEYWORDS
        
        self._initialize()
    
    def _initialize(self) -> None:
        """Initialize the OrchestratorAgent's routing model."""
        try:
            # Initialize routing model with lower temperature for consistent decisions
            self.llm = OllamaLLM(
                model=ModelConfig.ORCHESTRATOR_MODEL,
                temperature=ModelConfig.ORCHESTRATOR_TEMPERATURE
            )
            
            self._initialized = True
            logger.info("OrchestratorAgent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize OrchestratorAgent: {e}")
            raise AgentProcessingError(
                "Failed to initialize OrchestratorAgent",
                self.agent_type,
                e
            )
    
    def analyze_query_intent(self, message: str, has_image: bool = False) -> QueryIntent:
        """
        Analyze user query to determine intent and appropriate agent routing.
        
        Args:
            message: The user's query text
            has_image: Whether an image is included with the query
            
        Returns:
            QueryIntent enum indicating the detected intent
        """
        if not message:
            message = ""
        
        message_lower = message.lower()
        
        # Image analysis takes priority if image is present
        if has_image:
            if any(keyword in message_lower for keyword in self.multi_modal_keywords):
                return QueryIntent.MULTI_MODAL
            return QueryIntent.IMAGE_ANALYSIS
        
        # Check for specific search intents using keyword matching
        if any(keyword in message_lower for keyword in self.youtube_keywords):
            return QueryIntent.YOUTUBE_SEARCH
        
        if any(keyword in message_lower for keyword in self.web_search_keywords):
            return QueryIntent.WEB_SEARCH
        
        if any(keyword in message_lower for keyword in self.knowledge_base_keywords):
            return QueryIntent.KNOWLEDGE_BASE
        
        # Use LLM for more complex intent detection
        return self._llm_intent_analysis(message)
    
    def _llm_intent_analysis(self, message: str) -> QueryIntent:
        """
        Use LLM to analyze query intent when keyword matching is insufficient.
        
        Args:
            message: The user's query text
            
        Returns:
            QueryIntent enum based on LLM analysis
        """
        try:
            intent_prompt = PromptTemplates.INTENT_ANALYSIS_TEMPLATE.format(message=message)
            response = self.llm.invoke(intent_prompt).strip().upper()
            
            # Map response to enum
            intent_mapping = {
                "WEB_SEARCH": QueryIntent.WEB_SEARCH,
                "YOUTUBE_SEARCH": QueryIntent.YOUTUBE_SEARCH,
                "KNOWLEDGE_BASE": QueryIntent.KNOWLEDGE_BASE,
                "GENERAL_CHAT": QueryIntent.GENERAL_CHAT
            }
            
            return intent_mapping.get(response, QueryIntent.GENERAL_CHAT)
            
        except Exception as e:
            logger.warning(f"Intent analysis failed, defaulting to GENERAL_CHAT: {e}")
            return QueryIntent.GENERAL_CHAT
    
    def process_query(self, query: str, image_path: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Main orchestration method that routes queries to appropriate agents.
        
        Args:
            query: The user's query text
            image_path: Optional path to an image file
            **kwargs: Additional parameters
            
        Returns:
            Structured response with results from appropriate agent(s)
        """
        return self.route_query(query, image_path)
    
    def route_query(self, message: str, image_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Route queries to appropriate agents and coordinate responses.
        
        Args:
            message: The user's query text
            image_path: Optional path to an image file
            
        Returns:
            Coordinated response from appropriate agent(s)
        """
        try:
            has_image = image_path is not None
            intent = self.analyze_query_intent(message, has_image)
            
            logger.info(f"Query intent detected: {intent.value}")
            
            # Route based on intent
            if intent == QueryIntent.IMAGE_ANALYSIS:
                return self._handle_image_analysis(message, image_path)
            
            elif intent == QueryIntent.MULTI_MODAL:
                return self._handle_multi_modal_query(message, image_path)
            
            elif intent in [QueryIntent.WEB_SEARCH, QueryIntent.YOUTUBE_SEARCH,
                          QueryIntent.KNOWLEDGE_BASE, QueryIntent.GENERAL_CHAT]:
                return self._handle_text_query(message, intent)
            
            else:
                # Fallback to general chat
                return self._handle_text_query(message, QueryIntent.GENERAL_CHAT)
                
        except Exception as e:
            logger.error(f"Orchestrator routing error: {e}")
            return self._create_error_response(
                ErrorMessages.ORCHESTRATION_ERROR.format(error=str(e))
            )
    
    def _handle_image_analysis(self, message: str, image_path: str) -> Dict[str, Any]:
        """Handle image analysis queries."""
        try:
            query = message if message.strip() else "Describe this image in detail"
            result = self.image_agent.analyze_image(image_path, query)
            
            if result["success"]:
                return {
                    "success": True,
                    "response": result["response"],
                    "agents_used": [result["agent_type"]],
                    "intent": QueryIntent.IMAGE_ANALYSIS.value,
                    "metadata": {
                        "image_processed": True,
                        "query": query
                    }
                }
            else:
                # Provide fallback response
                fallback_response = FallbackMessages.IMAGE_ANALYSIS_FALLBACK
                return {
                    "success": False,
                    "response": f"{result['response']}\n\n{fallback_response}",
                    "agents_used": [result["agent_type"]],
                    "intent": QueryIntent.IMAGE_ANALYSIS.value,
                    "metadata": {
                        "image_processed": False,
                        "fallback_used": True,
                        "error": result["error"]
                    }
                }
                
        except Exception as e:
            logger.error(f"Image analysis handling error: {e}")
            return self._create_error_response(f"Image analysis error: {str(e)}")
    
    def _handle_text_query(self, message: str, intent: QueryIntent) -> Dict[str, Any]:
        """Handle text-based queries through TextAgent."""
        try:
            # Enhance message with intent context
            enhanced_message = self._enhance_message_with_intent(message, intent)
            result = self.text_agent.process_query(enhanced_message)
            
            if result["success"]:
                return {
                    "success": True,
                    "response": result["response"],
                    "agents_used": [result["agent_type"]],
                    "intent": intent.value,
                    "metadata": {
                        "tools_used": result.get("tools_used", []),
                        "enhanced_query": enhanced_message != message
                    }
                }
            else:
                # Implement retry logic
                return self._retry_text_query(message, intent)
                
        except Exception as e:
            logger.error(f"Text query handling error: {e}")
            return self._create_error_response(f"Text query error: {str(e)}")
    
    def _handle_multi_modal_query(self, message: str, image_path: str) -> Dict[str, Any]:
        """Handle queries that require both image and text analysis."""
        try:
            # First, analyze the image
            image_result = self.image_agent.analyze_image(image_path, message)
            
            # Then, use image analysis to enhance text query
            if image_result["success"]:
                enhanced_message = f"{message}\n\nImage analysis context: {image_result['response']}"
                text_result = self.text_agent.process_query(enhanced_message)
                
                # Combine responses
                combined_response = self._combine_responses([image_result, text_result])
                
                return {
                    "success": True,
                    "response": combined_response,
                    "agents_used": [image_result["agent_type"], text_result.get("agent_type", "text_rag")],
                    "intent": QueryIntent.MULTI_MODAL.value,
                    "metadata": {
                        "image_processed": True,
                        "text_processed": text_result.get("success", False),
                        "tools_used": text_result.get("tools_used", [])
                    }
                }
            else:
                # Fallback to text-only
                return self._handle_text_query(message, QueryIntent.GENERAL_CHAT)
                
        except Exception as e:
            logger.error(f"Multi-modal query handling error: {e}")
            return self._create_error_response(f"Multi-modal query error: {str(e)}")
    
    def _enhance_message_with_intent(self, message: str, intent: QueryIntent) -> str:
        """Enhance message with intent-specific context."""
        prefix = RetryConfig.INTENT_PREFIXES.get(intent, "")
        return f"{prefix}{message}" if prefix else message
    
    def _retry_text_query(self, message: str, intent: QueryIntent) -> Dict[str, Any]:
        """Implement retry logic with fallback strategies."""
        for attempt in range(RetryConfig.MAX_RETRIES):
            try:
                simplified_message = self._simplify_message(message)
                result = self.text_agent.process_query(simplified_message)
                
                if result["success"]:
                    return {
                        "success": True,
                        "response": result["response"],
                        "agents_used": [result["agent_type"]],
                        "intent": intent.value,
                        "metadata": {
                            "retry_attempt": attempt + 1,
                            "simplified_query": True,
                            "tools_used": result.get("tools_used", [])
                        }
                    }
                    
            except Exception as e:
                logger.warning(f"Retry attempt {attempt + 1} failed: {e}")
                continue
        
        # Final fallback
        return {
            "success": False,
            "response": ErrorMessages.RETRY_EXCEEDED,
            "agents_used": ["orchestrator"],
            "intent": intent.value,
            "metadata": {
                "max_retries_exceeded": True,
                "fallback_response": True
            }
        }
    
    def _simplify_message(self, message: str) -> str:
        """Simplify complex messages for retry attempts."""
        simplified = re.sub(r'[^\w\s]', ' ', message)
        simplified = ' '.join(simplified.split())
        return simplified[:RetryConfig.SIMPLIFIED_MESSAGE_MAX_LENGTH]
    
    def _combine_responses(self, results: List[Dict[str, Any]]) -> str:
        """Combine responses from multiple agents."""
        successful_responses = [r["response"] for r in results if r.get("success", False)]
        
        if len(successful_responses) == 1:
            return successful_responses[0]
        elif len(successful_responses) > 1:
            return (f"Based on my analysis:\n\n"
                   f"🖼️ **Image Analysis:**\n{successful_responses[0]}\n\n"
                   f"🔍 **Additional Information:**\n{successful_responses[1]}")
        else:
            return ErrorMessages.MULTI_AGENT_ERROR
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status information about all managed agents."""
        return {
            "orchestrator": {
                "status": "active" if self._initialized else "initializing",
                "capabilities": self.capabilities
            },
            "text_agent": self.text_agent.get_status(),
            "image_agent": self.image_agent.get_status()
        }

#!/usr/bin/env python3
"""
Debug script to test file handling in the ChatInterface.

This script will help identify issues with file processing in the chat_function.
"""

import tempfile
import os
from pathlib import Path
from app import MultiAgentRAGApp

def create_test_files():
    """Create temporary test files for debugging"""
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    
    # Create a test PDF file (mock)
    pdf_path = os.path.join(temp_dir, "test_document.pdf")
    with open(pdf_path, "w") as f:
        f.write("This is a test PDF content for debugging.")
    
    # Create a test image file (mock)
    image_path = os.path.join(temp_dir, "test_image.jpg")
    with open(image_path, "w") as f:
        f.write("Mock image content")
    
    # Create a test text file
    txt_path = os.path.join(temp_dir, "test_document.txt")
    with open(txt_path, "w") as f:
        f.write("This is a test text document for RAG functionality testing.")
    
    return temp_dir, pdf_path, image_path, txt_path

def test_file_type_detection():
    """Test file type detection"""
    print("🔍 Testing File Type Detection...")
    print("=" * 50)
    
    app = MultiAgentRAGApp()
    
    test_cases = [
        ("document.pdf", "document"),
        ("image.jpg", "image"),
        ("text.txt", "document"),
        ("unknown.xyz", "unsupported"),
        ("", "none"),
        (None, "none")
    ]
    
    for file_path, expected in test_cases:
        result = app.detect_file_type(file_path)
        status = "✅" if result == expected else "❌"
        print(f"{status} {file_path or 'None'} -> {result} (expected: {expected})")

def test_file_processing():
    """Test file processing with real files"""
    print("\n📄 Testing File Processing...")
    print("=" * 50)
    
    app = MultiAgentRAGApp()
    temp_dir, pdf_path, image_path, txt_path = create_test_files()
    
    try:
        # Test text file processing
        print("Testing text file processing...")
        result = app.process_uploaded_file(txt_path)
        print(f"Text file result: {result}")
        
        # Test PDF file processing (will likely fail due to mock content)
        print("\nTesting PDF file processing...")
        result = app.process_uploaded_file(pdf_path)
        print(f"PDF file result: {result}")
        
        # Test image file processing
        print("\nTesting image file processing...")
        result = app.process_uploaded_file(image_path)
        print(f"Image file result: {result}")
        
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

def test_chat_function_with_files():
    """Test chat function with mock file objects"""
    print("\n💬 Testing Chat Function with Files...")
    print("=" * 50)
    
    app = MultiAgentRAGApp()
    temp_dir, pdf_path, image_path, txt_path = create_test_files()
    
    try:
        # Mock file objects (simulating what ChatInterface might pass)
        class MockFile:
            def __init__(self, path):
                self.name = path
                self.path = path
        
        # Test with text file
        print("Testing chat function with text file...")
        mock_txt_file = MockFile(txt_path)
        result = app.chat_function(
            message="What does this document say?",
            history=[],
            files=[mock_txt_file]
        )
        print(f"Text file chat result: {result[:200]}...")
        
        # Test with image file
        print("\nTesting chat function with image file...")
        mock_image_file = MockFile(image_path)
        result = app.chat_function(
            message="What do you see in this image?",
            history=[],
            files=[mock_image_file]
        )
        print(f"Image file chat result: {result[:200]}...")
        
        # Test with no message but files
        print("\nTesting chat function with files but no message...")
        result = app.chat_function(
            message="",
            history=[],
            files=[mock_txt_file]
        )
        print(f"No message result: {result[:200]}...")
        
    except Exception as e:
        print(f"❌ Error in chat function test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

def test_file_parameter_formats():
    """Test different file parameter formats that ChatInterface might use"""
    print("\n🔧 Testing File Parameter Formats...")
    print("=" * 50)
    
    app = MultiAgentRAGApp()
    temp_dir, pdf_path, image_path, txt_path = create_test_files()
    
    try:
        # Test different file object formats
        formats_to_test = [
            # Format 1: Simple path string
            txt_path,
            
            # Format 2: Object with name attribute
            type('MockFile', (), {'name': txt_path})(),
            
            # Format 3: Object with path attribute
            type('MockFile', (), {'path': txt_path})(),
            
            # Format 4: Dictionary format
            {'name': txt_path, 'path': txt_path},
        ]
        
        for i, file_obj in enumerate(formats_to_test):
            print(f"\nTesting format {i+1}: {type(file_obj)}")
            try:
                # Test file path extraction
                if hasattr(file_obj, 'name'):
                    file_path = file_obj.name
                elif hasattr(file_obj, 'path'):
                    file_path = file_obj.path
                elif isinstance(file_obj, dict):
                    file_path = file_obj.get('name') or file_obj.get('path')
                else:
                    file_path = str(file_obj)
                
                print(f"  Extracted path: {file_path}")
                
                # Test file type detection
                file_type = app.detect_file_type(file_path)
                print(f"  Detected type: {file_type}")
                
            except Exception as e:
                print(f"  ❌ Error with format {i+1}: {e}")
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

def main():
    """Run all debugging tests"""
    print("🚀 Debugging File Handling in ChatInterface")
    print("=" * 60)
    
    try:
        test_file_type_detection()
        test_file_processing()
        test_chat_function_with_files()
        test_file_parameter_formats()
        
        print("\n" + "=" * 60)
        print("🎯 Debug Summary:")
        print("   • File type detection working")
        print("   • File processing needs real file content")
        print("   • Chat function file handling needs verification")
        print("   • Multiple file object formats should be supported")
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

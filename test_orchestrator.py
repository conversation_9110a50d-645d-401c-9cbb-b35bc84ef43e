#!/usr/bin/env python3
"""
Test script for the OrchestratorAgent to verify functionality
"""

import sys
import os
from agents import TextAgent, ImageAgent, OrchestratorAgent, QueryIntent

def test_orchestrator():
    """Test the orchestrator agent functionality"""
    print("🧪 Testing OrchestratorAgent...")
    
    try:
        # Initialize agents
        print("1. Initializing agents...")
        text_agent = TextAgent()
        image_agent = ImageAgent()
        orchestrator = OrchestratorAgent(text_agent, image_agent)
        print("✅ Agents initialized successfully")
        
        # Test intent analysis
        print("\n2. Testing intent analysis...")
        test_queries = [
            ("What's the latest news about AI?", False, QueryIntent.WEB_SEARCH),
            ("Find YouTube videos about Python", False, QueryIntent.YOUTUBE_SEARCH),
            ("Summarize my uploaded documents", False, QueryIntent.KNOWLEDGE_BASE),
            ("Hello, how are you?", False, QueryIntent.GENERAL_CHAT),
            ("Analyze this image", True, QueryIntent.IMAGE_ANALYSIS),
        ]
        
        for query, has_image, expected_intent in test_queries:
            detected_intent = orchestrator.analyze_query_intent(query, has_image)
            status = "✅" if detected_intent == expected_intent else "❌"
            print(f"   {status} '{query}' -> {detected_intent.value} (expected: {expected_intent.value})")
        
        # Test text query routing
        print("\n3. Testing text query routing...")
        test_text_query = "What is machine learning?"
        result = orchestrator.route_query(test_text_query)
        
        if result["success"]:
            print(f"✅ Text query successful")
            print(f"   Response preview: {result['response'][:100]}...")
            print(f"   Agents used: {result['agents_used']}")
            print(f"   Intent: {result['intent']}")
        else:
            print(f"❌ Text query failed: {result['response']}")
        
        # Test agent status
        print("\n4. Testing agent status...")
        status = orchestrator.get_agent_status()
        print("✅ Agent status retrieved:")
        for agent_name, agent_info in status.items():
            print(f"   {agent_name}: {agent_info['status']} ({len(agent_info['capabilities'])} capabilities)")
        
        print("\n🎉 All orchestrator tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_examples():
    """Test with example queries that showcase orchestrator capabilities"""
    print("\n🔍 Testing example queries...")
    
    try:
        text_agent = TextAgent()
        image_agent = ImageAgent()
        orchestrator = OrchestratorAgent(text_agent, image_agent)
        
        example_queries = [
            "What's the current weather?",
            "Find videos about cooking pasta",
            "Explain quantum physics",
            "Search for recent AI breakthroughs"
        ]
        
        for query in example_queries:
            print(f"\n📝 Testing: '{query}'")
            result = orchestrator.route_query(query)
            
            print(f"   Intent: {result.get('intent', 'unknown')}")
            print(f"   Success: {result.get('success', False)}")
            print(f"   Agents: {result.get('agents_used', [])}")
            
            if result.get('metadata', {}).get('tools_used'):
                print(f"   Tools: {result['metadata']['tools_used']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Example query test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Orchestrator Tests...")
    
    # Run basic functionality tests
    basic_test_passed = test_orchestrator()
    
    # Run example query tests
    example_test_passed = test_query_examples()
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"   Basic functionality: {'✅ PASSED' if basic_test_passed else '❌ FAILED'}")
    print(f"   Example queries: {'✅ PASSED' if example_test_passed else '❌ FAILED'}")
    
    if basic_test_passed and example_test_passed:
        print("\n🎉 All tests passed! The orchestrator is ready to use.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
        sys.exit(1)

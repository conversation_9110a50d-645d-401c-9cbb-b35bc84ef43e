#!/usr/bin/env python3
"""
Example usage of the refactored Multi-Agent RAG system.

This script demonstrates how to use the new modular architecture
with different approaches for various use cases.
"""

import logging
from typing import Dict, Any

# Configure logging to see agent activity
logging.basicConfig(level=logging.INFO)

def example_1_simple_usage():
    """Example 1: Simple usage with backward compatibility"""
    print("🔹 Example 1: Simple Usage (Backward Compatible)")
    print("=" * 50)
    
    # This import pattern works exactly as before the refactoring
    from agents import TextAgent, ImageAgent, OrchestratorAgent
    
    # Initialize agents (same as before)
    text_agent = TextAgent()
    image_agent = ImageAgent()
    orchestrator = OrchestratorAgent(text_agent, image_agent)
    
    # Test a simple query
    result = orchestrator.route_query("What's the weather like today?")
    print(f"Query: What's the weather like today?")
    print(f"Intent: {result.get('intent', 'unknown')}")
    print(f"Success: {result.get('success', False)}")
    print(f"Agents used: {result.get('agents_used', [])}")
    print()


def example_2_factory_function():
    """Example 2: Using the new factory function"""
    print("🔹 Example 2: Factory Function Usage")
    print("=" * 50)
    
    # Use the new factory function for convenience
    from agents import create_orchestrator_system
    
    # Create complete system with one function call
    orchestrator = create_orchestrator_system()
    
    # Test multiple queries to show intent detection
    test_queries = [
        "Find YouTube videos about Python programming",
        "What's the latest news about AI?",
        "Hello, how are you today?",
    ]
    
    for query in test_queries:
        result = orchestrator.route_query(query)
        print(f"Query: {query}")
        print(f"Intent: {result.get('intent', 'unknown')}")
        print(f"Agents: {result.get('agents_used', [])}")
        print()


def example_3_individual_agents():
    """Example 3: Using individual agents directly"""
    print("🔹 Example 3: Individual Agent Usage")
    print("=" * 50)
    
    # Import specific agents
    from agents.text_agent import TextAgent
    from agents.image_agent import ImageAgent
    
    # Use TextAgent directly
    text_agent = TextAgent()
    result = text_agent.process_query("Explain machine learning")
    print(f"TextAgent result: {result.get('success', False)}")
    print(f"Tools used: {result.get('tools_used', [])}")
    print()
    
    # Use ImageAgent directly (would need an actual image file)
    image_agent = ImageAgent()
    print(f"ImageAgent capabilities: {image_agent.get_capabilities()}")
    print(f"ImageAgent status: {image_agent.get_status()}")
    print()


def example_4_configuration_usage():
    """Example 4: Using the configuration module"""
    print("🔹 Example 4: Configuration Module Usage")
    print("=" * 50)
    
    # Import configuration
    from config import ModelConfig, IntentDetectionConfig, AgentCapabilities
    
    print(f"Text Model: {ModelConfig.TEXT_MODEL}")
    print(f"Image Model: {ModelConfig.IMAGE_MODEL}")
    print(f"Web Search Keywords: {IntentDetectionConfig.WEB_SEARCH_KEYWORDS[:3]}...")
    print(f"Text Agent Capabilities: {AgentCapabilities.TEXT_AGENT_CAPABILITIES}")
    print()


def example_5_agent_status_monitoring():
    """Example 5: Agent status and monitoring"""
    print("🔹 Example 5: Agent Status Monitoring")
    print("=" * 50)
    
    from agents import create_orchestrator_system
    
    # Create system
    orchestrator = create_orchestrator_system()
    
    # Get comprehensive status
    status = orchestrator.get_agent_status()
    
    print("System Status:")
    for agent_name, agent_info in status.items():
        print(f"  {agent_name}:")
        print(f"    Status: {agent_info.get('status', 'unknown')}")
        print(f"    Capabilities: {len(agent_info.get('capabilities', []))} features")
    print()


def example_6_error_handling():
    """Example 6: Error handling and fallback behavior"""
    print("🔹 Example 6: Error Handling")
    print("=" * 50)
    
    from agents import create_orchestrator_system
    
    orchestrator = create_orchestrator_system()
    
    # Test with a potentially problematic query
    result = orchestrator.route_query("")  # Empty query
    
    print(f"Empty query handling:")
    print(f"Success: {result.get('success', False)}")
    print(f"Response: {result.get('response', 'No response')[:100]}...")
    print(f"Metadata: {result.get('metadata', {})}")
    print()


def example_7_package_info():
    """Example 7: Package information and utilities"""
    print("🔹 Example 7: Package Information")
    print("=" * 50)
    
    from agents import get_available_agents, __version__, __author__
    
    print(f"Package Version: {__version__}")
    print(f"Package Author: {__author__}")
    print()
    
    print("Available Agents:")
    agents_info = get_available_agents()
    for agent_name, info in agents_info.items():
        print(f"  {agent_name}: {info['description']}")
    print()


def main():
    """Run all examples"""
    print("🚀 Multi-Agent RAG System - Refactored Architecture Examples")
    print("=" * 60)
    print()
    
    examples = [
        example_1_simple_usage,
        example_2_factory_function,
        example_3_individual_agents,
        example_4_configuration_usage,
        example_5_agent_status_monitoring,
        example_6_error_handling,
        example_7_package_info,
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            example_func()
        except Exception as e:
            print(f"❌ Example {i} failed: {e}")
            print()
        
        if i < len(examples):
            input("Press Enter to continue to next example...")
            print()
    
    print("🎉 All examples completed!")
    print()
    print("💡 Key Benefits of Refactored Architecture:")
    print("   ✅ Modular design for better maintainability")
    print("   ✅ Centralized configuration management")
    print("   ✅ Type safety with comprehensive type hints")
    print("   ✅ Backward compatibility with existing code")
    print("   ✅ Easy extensibility for new agents")
    print("   ✅ Better error handling and monitoring")


if __name__ == "__main__":
    main()

"""
Base agent class for the Multi-Agent RAG system.

This module defines the abstract base class that all specialized agents inherit from,
ensuring consistent interfaces and behavior across the system.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List
import logging

from config import AgentType

# Configure logging
logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """
    Abstract base class for all agents in the Multi-Agent RAG system.
    
    This class defines the common interface and shared functionality that all
    specialized agents must implement or can inherit.
    """
    
    def __init__(self, agent_type: AgentType, capabilities: List[str]):
        """
        Initialize the base agent.
        
        Args:
            agent_type: The type of agent (from AgentType enum)
            capabilities: List of capabilities this agent provides
        """
        self.agent_type = agent_type
        self.capabilities = capabilities
        self._initialized = False
        
        logger.info(f"Initializing {agent_type.value} agent with capabilities: {capabilities}")
    
    @abstractmethod
    def _initialize(self) -> None:
        """
        Initialize agent-specific components.
        
        This method should be implemented by each specialized agent to set up
        their specific models, tools, and configurations.
        """
        pass
    
    @abstractmethod
    def process_query(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Process a query and return a structured response.
        
        Args:
            query: The input query to process
            **kwargs: Additional parameters specific to the agent
            
        Returns:
            Dict containing:
                - success: bool indicating if processing was successful
                - response: str with the agent's response
                - agent_type: str identifying which agent processed the query
                - metadata: dict with additional processing information
                - error: str with error details if success is False
        """
        pass
    
    def get_capabilities(self) -> List[str]:
        """
        Get the list of capabilities this agent provides.
        
        Returns:
            List of capability strings
        """
        return self.capabilities.copy()
    
    def get_agent_type(self) -> AgentType:
        """
        Get the agent type.
        
        Returns:
            AgentType enum value
        """
        return self.agent_type
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the agent.
        
        Returns:
            Dict containing agent status information
        """
        return {
            "agent_type": self.agent_type.value,
            "capabilities": self.capabilities,
            "initialized": self._initialized,
            "status": "active" if self._initialized else "initializing"
        }
    
    def _create_success_response(self, response: str, **metadata) -> Dict[str, Any]:
        """
        Create a standardized success response.
        
        Args:
            response: The response content
            **metadata: Additional metadata to include
            
        Returns:
            Standardized success response dictionary
        """
        return {
            "success": True,
            "response": response,
            "agent_type": self.agent_type.value,
            "metadata": metadata,
            "error": None
        }
    
    def _create_error_response(self, error_message: str, **metadata) -> Dict[str, Any]:
        """
        Create a standardized error response.
        
        Args:
            error_message: The error message
            **metadata: Additional metadata to include
            
        Returns:
            Standardized error response dictionary
        """
        logger.error(f"{self.agent_type.value} error: {error_message}")
        return {
            "success": False,
            "response": error_message,
            "agent_type": self.agent_type.value,
            "metadata": metadata,
            "error": error_message
        }
    
    def _log_processing(self, query: str) -> None:
        """
        Log that the agent is processing a query.
        
        Args:
            query: The query being processed
        """
        truncated_query = query[:100] + "..." if len(query) > 100 else query
        logger.info(f"{self.agent_type.value} processing query: {truncated_query}")


class AgentError(Exception):
    """Custom exception for agent-related errors."""
    
    def __init__(self, message: str, agent_type: AgentType, original_error: Exception = None):
        """
        Initialize the agent error.
        
        Args:
            message: Error message
            agent_type: The agent type that encountered the error
            original_error: The original exception that caused this error
        """
        self.agent_type = agent_type
        self.original_error = original_error
        super().__init__(f"{agent_type.value}: {message}")


class AgentInitializationError(AgentError):
    """Exception raised when an agent fails to initialize."""
    pass


class AgentProcessingError(AgentError):
    """Exception raised when an agent fails to process a query."""
    pass

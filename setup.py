import subprocess
import sys

def install_ollama_models():
    """Install required Ollama models"""
    models = [
        "qwen3:0.6b",
        "qwen2.5vl:3b"
    ]
    
    print("Installing Ollama models...")
    
    for model in models:
        print(f"Installing {model}...")
        try:
            subprocess.run(["ollama", "pull", model], check=True)
            print(f"✅ {model} installed successfully")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {model}")
        except FileNotFoundError:
            print("❌ Ollama not found. Please install Ollama first.")
            return False
    
    return True

if __name__ == "__main__":
    print("Setting up Multi-Agent RAG System...")
    
    # Install Python dependencies
    print("Installing Python dependencies...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    
    # Install Ollama models
    install_ollama_models()
    
    print("\nSetup complete! Run 'python app.py' to start the application.")

#!/usr/bin/env python3
"""
Test script for the streamlined ImageAgent focused on visual question answering.

This script verifies that the ImageAgent now focuses exclusively on:
1. Visual question answering with specific questions
2. General image description when no specific question is provided
"""

import sys
import logging
from agents.image_agent import ImageAgent
from config import AgentCapabilities

# Configure logging
logging.basicConfig(level=logging.INFO)

def test_streamlined_capabilities():
    """Test that the ImageAgent has only the expected capabilities"""
    print("🔍 Testing ImageAgent Capabilities...")
    print("=" * 50)
    
    try:
        image_agent = ImageAgent()
        capabilities = image_agent.get_capabilities()
        expected_capabilities = AgentCapabilities.IMAGE_AGENT_CAPABILITIES
        
        print(f"Expected capabilities: {expected_capabilities}")
        print(f"Actual capabilities: {capabilities}")
        
        # Check that capabilities match expected streamlined set
        if set(capabilities) == set(expected_capabilities):
            print("✅ Capabilities match expected streamlined set")
        else:
            print("❌ Capabilities don't match expected set")
            return False
        
        # Verify specific capabilities
        required_capabilities = ["visual_question_answering", "image_description", "image_analysis"]
        for capability in required_capabilities:
            if capability in capabilities:
                print(f"✅ Has required capability: {capability}")
            else:
                print(f"❌ Missing required capability: {capability}")
                return False
        
        # Verify removed capabilities are not present
        removed_capabilities = ["image_comparison", "text_extraction_from_images"]
        for capability in removed_capabilities:
            if capability not in capabilities:
                print(f"✅ Correctly removed capability: {capability}")
            else:
                print(f"❌ Still has removed capability: {capability}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Capability test failed: {e}")
        return False

def test_available_methods():
    """Test that only expected methods are available"""
    print("\n🔧 Testing Available Methods...")
    print("=" * 50)
    
    try:
        image_agent = ImageAgent()
        
        # Check for required methods
        required_methods = [
            "analyze_image",
            "answer_visual_question", 
            "describe_image",
            "process_query"
        ]
        
        for method_name in required_methods:
            if hasattr(image_agent, method_name):
                print(f"✅ Has required method: {method_name}")
            else:
                print(f"❌ Missing required method: {method_name}")
                return False
        
        # Check that removed methods are not available
        removed_methods = [
            "compare_images",
            "extract_text_from_image",
            "get_image_metadata",
            "describe_image_content",  # Renamed to describe_image
            "encode_image"
        ]
        
        for method_name in removed_methods:
            if not hasattr(image_agent, method_name):
                print(f"✅ Correctly removed method: {method_name}")
            else:
                print(f"❌ Still has removed method: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Method test failed: {e}")
        return False

def test_visual_question_answering():
    """Test the visual question answering functionality"""
    print("\n❓ Testing Visual Question Answering...")
    print("=" * 50)
    
    try:
        image_agent = ImageAgent()
        
        # Test with a mock image path (would need real image in production)
        test_image_path = "test_image.jpg"
        test_question = "What objects can you see in this image?"
        
        print(f"Testing question: '{test_question}'")
        
        # Test answer_visual_question method
        result = image_agent.answer_visual_question(test_image_path, test_question)
        
        # Check response structure
        required_keys = ["success", "response", "agent_type", "metadata", "error"]
        for key in required_keys:
            if key in result:
                print(f"✅ Response has required key: {key}")
            else:
                print(f"❌ Response missing key: {key}")
                return False
        
        print(f"Agent type: {result.get('agent_type', 'unknown')}")
        print(f"Success: {result.get('success', False)}")
        
        # Note: This will likely fail without a real image, but structure should be correct
        if not result.get('success', False):
            print("ℹ️  Expected failure without real image file")
        
        return True
        
    except Exception as e:
        print(f"❌ Visual question answering test failed: {e}")
        return False

def test_image_description():
    """Test the general image description functionality"""
    print("\n📝 Testing Image Description...")
    print("=" * 50)
    
    try:
        image_agent = ImageAgent()
        
        # Test with a mock image path
        test_image_path = "test_image.jpg"
        
        print("Testing general image description...")
        
        # Test describe_image method
        result = image_agent.describe_image(test_image_path)
        
        # Check response structure
        required_keys = ["success", "response", "agent_type", "metadata", "error"]
        for key in required_keys:
            if key in result:
                print(f"✅ Response has required key: {key}")
            else:
                print(f"❌ Response missing key: {key}")
                return False
        
        print(f"Agent type: {result.get('agent_type', 'unknown')}")
        print(f"Success: {result.get('success', False)}")
        
        # Note: This will likely fail without a real image, but structure should be correct
        if not result.get('success', False):
            print("ℹ️  Expected failure without real image file")
        
        return True
        
    except Exception as e:
        print(f"❌ Image description test failed: {e}")
        return False

def test_process_query_interface():
    """Test the process_query interface for backward compatibility"""
    print("\n🔄 Testing Process Query Interface...")
    print("=" * 50)
    
    try:
        image_agent = ImageAgent()
        
        # Test process_query method
        test_query = "What colors are prominent in this image?"
        test_image_path = "test_image.jpg"
        
        print(f"Testing query: '{test_query}'")
        
        result = image_agent.process_query(test_query, test_image_path)
        
        # Check response structure
        required_keys = ["success", "response", "agent_type", "metadata", "error"]
        for key in required_keys:
            if key in result:
                print(f"✅ Response has required key: {key}")
            else:
                print(f"❌ Response missing key: {key}")
                return False
        
        # Test error handling when no image path provided
        result_no_image = image_agent.process_query(test_query)
        
        if not result_no_image.get('success', True):
            print("✅ Correctly handles missing image path")
        else:
            print("❌ Should fail when no image path provided")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Process query test failed: {e}")
        return False

def test_agent_status():
    """Test agent status and metadata"""
    print("\n📊 Testing Agent Status...")
    print("=" * 50)
    
    try:
        image_agent = ImageAgent()
        
        # Test get_status method
        status = image_agent.get_status()
        
        print(f"Agent status: {status}")
        
        # Check status structure
        required_keys = ["agent_type", "capabilities", "initialized", "status"]
        for key in required_keys:
            if key in status:
                print(f"✅ Status has required key: {key}")
            else:
                print(f"❌ Status missing key: {key}")
                return False
        
        # Verify agent type
        if status.get('agent_type') == 'image':
            print("✅ Correct agent type")
        else:
            print(f"❌ Incorrect agent type: {status.get('agent_type')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent status test failed: {e}")
        return False

def main():
    """Run all tests for the streamlined ImageAgent"""
    print("🚀 Testing Streamlined ImageAgent - Visual Question Answering Focus")
    print("=" * 70)
    print()
    
    tests = [
        ("Capabilities", test_streamlined_capabilities),
        ("Available Methods", test_available_methods),
        ("Visual Question Answering", test_visual_question_answering),
        ("Image Description", test_image_description),
        ("Process Query Interface", test_process_query_interface),
        ("Agent Status", test_agent_status),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running test: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} - PASSED")
                passed_tests += 1
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
        
        print()
    
    # Summary
    print("=" * 70)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! ImageAgent successfully streamlined for visual question answering.")
        print()
        print("✅ Key Features Verified:")
        print("   • Focused on visual question answering and image description")
        print("   • Removed unnecessary image processing methods")
        print("   • Maintained backward compatibility with orchestrator")
        print("   • Structured responses with proper metadata")
        print("   • Error handling for missing image paths")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()

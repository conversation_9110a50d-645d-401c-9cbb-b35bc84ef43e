from langchain_community.document_loaders import Text<PERSON>oader, PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import os

class DocumentProcessor:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len
        )
    
    def process_uploaded_files(self, file_paths):
        documents = []
        
        for file_path in file_paths:
            if file_path.endswith('.txt'):
                loader = TextLoader(file_path)
                docs = loader.load()
                documents.extend(docs)
            elif file_path.endswith('.pdf'):
                loader = PyPDFLoader(file_path)
                docs = loader.load()
                documents.extend(docs)
        
        # Split documents into chunks
        split_docs = self.text_splitter.split_documents(documents)
        return split_docs
    
    def process_text_input(self, text: str, source: str = "user_input"):
        doc = Document(page_content=text, metadata={"source": source})
        split_docs = self.text_splitter.split_documents([doc])
        return split_docs

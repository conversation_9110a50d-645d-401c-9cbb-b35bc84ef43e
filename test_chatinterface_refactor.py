#!/usr/bin/env python3
"""
Test script for the refactored Multi-Agent RAG system with gr.ChatInterface.

This script verifies that the new ChatInterface implementation:
1. Handles multi-modal file uploads (PDF, images)
2. Routes queries correctly through the orchestrator
3. Maintains backward compatibility with all agent functionality
4. Provides proper file processing feedback
"""

import sys
import tempfile
from pathlib import Path
from app import MultiAgentRAGApp

def test_app_initialization():
    """Test that the MultiAgentRAGApp initializes correctly"""
    print("🔧 Testing App Initialization...")
    print("=" * 50)
    
    try:
        app = MultiAgentRAGApp()
        
        # Check that all components are initialized
        assert hasattr(app, 'text_agent'), "TextAgent not initialized"
        assert hasattr(app, 'image_agent'), "ImageAgent not initialized"
        assert hasattr(app, 'orchestrator'), "OrchestratorAgent not initialized"
        assert hasattr(app, 'doc_processor'), "DocumentProcessor not initialized"
        
        print("✅ All agents initialized successfully")
        
        # Check file type detection
        assert hasattr(app, 'supported_image_types'), "Image types not defined"
        assert hasattr(app, 'supported_document_types'), "Document types not defined"
        
        print("✅ File type detection configured")
        
        return True
        
    except Exception as e:
        print(f"❌ App initialization failed: {e}")
        return False

def test_file_type_detection():
    """Test file type detection functionality"""
    print("\n📁 Testing File Type Detection...")
    print("=" * 50)
    
    try:
        app = MultiAgentRAGApp()
        
        # Test image file detection
        test_cases = [
            ("test.jpg", "image"),
            ("test.jpeg", "image"),
            ("test.png", "image"),
            ("test.gif", "image"),
            ("document.pdf", "document"),
            ("document.txt", "document"),
            ("document.md", "document"),
            ("unknown.xyz", "unsupported"),
            ("", "none"),
            (None, "none")
        ]
        
        for file_path, expected_type in test_cases:
            if file_path is None:
                detected_type = app.detect_file_type(None)
            else:
                detected_type = app.detect_file_type(file_path)
            
            if detected_type == expected_type:
                print(f"✅ {file_path or 'None'} -> {detected_type}")
            else:
                print(f"❌ {file_path or 'None'} -> {detected_type} (expected: {expected_type})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ File type detection test failed: {e}")
        return False

def test_chat_function_interface():
    """Test the chat function interface"""
    print("\n💬 Testing Chat Function Interface...")
    print("=" * 50)
    
    try:
        app = MultiAgentRAGApp()
        
        # Test basic text query
        response = app.chat_function("Hello, how are you?", [], None)
        
        if isinstance(response, str) and len(response) > 0:
            print("✅ Basic text query handled correctly")
            print(f"   Response preview: {response[:100]}...")
        else:
            print("❌ Basic text query failed")
            return False
        
        # Test empty message handling
        response = app.chat_function("", [], None)
        
        if isinstance(response, str):
            print("✅ Empty message handled gracefully")
        else:
            print("❌ Empty message handling failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chat function test failed: {e}")
        return False

def test_file_processing_simulation():
    """Test file processing simulation (without actual files)"""
    print("\n📄 Testing File Processing Simulation...")
    print("=" * 50)
    
    try:
        app = MultiAgentRAGApp()
        
        # Simulate file objects
        class MockFile:
            def __init__(self, name):
                self.name = name
        
        # Test document processing message
        mock_pdf = MockFile("test_document.pdf")
        result = app.process_uploaded_file(mock_pdf.name)
        
        if "Document Processed" in result or "Error Processing" in result:
            print("✅ Document processing message generated")
            print(f"   Result: {result}")
        else:
            print("❌ Document processing message failed")
            return False
        
        # Test image processing message
        mock_image = MockFile("test_image.jpg")
        result = app.process_uploaded_file(mock_image.name)
        
        if "Image Uploaded" in result:
            print("✅ Image processing message generated")
            print(f"   Result: {result}")
        else:
            print("❌ Image processing message failed")
            return False
        
        # Test unsupported file
        mock_unsupported = MockFile("test.xyz")
        result = app.process_uploaded_file(mock_unsupported.name)
        
        if "Unsupported File" in result:
            print("✅ Unsupported file message generated")
            print(f"   Result: {result}")
        else:
            print("❌ Unsupported file message failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ File processing simulation failed: {e}")
        return False

def test_orchestrator_integration():
    """Test orchestrator integration with chat function"""
    print("\n🎯 Testing Orchestrator Integration...")
    print("=" * 50)
    
    try:
        app = MultiAgentRAGApp()
        
        # Test different query types
        test_queries = [
            ("What's the weather today?", "web search"),
            ("Find videos about Python", "youtube search"),
            ("Hello there", "general chat"),
        ]
        
        for query, expected_intent in test_queries:
            response = app.chat_function(query, [], None)
            
            if isinstance(response, str) and len(response) > 0:
                print(f"✅ '{query}' -> Response generated ({expected_intent})")
            else:
                print(f"❌ '{query}' -> No response ({expected_intent})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator integration test failed: {e}")
        return False

def test_multi_modal_simulation():
    """Test multi-modal query simulation"""
    print("\n🔄 Testing Multi-Modal Query Simulation...")
    print("=" * 50)
    
    try:
        app = MultiAgentRAGApp()
        
        # Simulate file with image
        class MockFile:
            def __init__(self, name):
                self.name = name
        
        mock_image = MockFile("test_chart.png")
        
        # Test multi-modal query (image + text)
        response = app.chat_function(
            "What do you see in this image?", 
            [], 
            [mock_image]
        )
        
        if isinstance(response, str) and len(response) > 0:
            print("✅ Multi-modal query handled")
            print(f"   Response preview: {response[:150]}...")
            
            # Check if file processing message is included
            if "Image Uploaded" in response:
                print("✅ File processing feedback included")
            else:
                print("ℹ️  File processing feedback not found (may be processed differently)")
        else:
            print("❌ Multi-modal query failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Multi-modal query test failed: {e}")
        return False

def test_error_handling():
    """Test error handling in chat function"""
    print("\n🛡️ Testing Error Handling...")
    print("=" * 50)
    
    try:
        app = MultiAgentRAGApp()
        
        # Test with invalid file list
        response = app.chat_function("Test query", [], "invalid_file_list")
        
        if isinstance(response, str):
            print("✅ Invalid file list handled gracefully")
        else:
            print("❌ Invalid file list not handled properly")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def main():
    """Run all tests for the ChatInterface refactor"""
    print("🚀 Testing ChatInterface Refactor - Multi-Modal File Support")
    print("=" * 70)
    print()
    
    tests = [
        ("App Initialization", test_app_initialization),
        ("File Type Detection", test_file_type_detection),
        ("Chat Function Interface", test_chat_function_interface),
        ("File Processing Simulation", test_file_processing_simulation),
        ("Orchestrator Integration", test_orchestrator_integration),
        ("Multi-Modal Simulation", test_multi_modal_simulation),
        ("Error Handling", test_error_handling),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running test: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} - PASSED")
                passed_tests += 1
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
        
        print()
    
    # Summary
    print("=" * 70)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! ChatInterface refactor successful.")
        print()
        print("✅ Key Features Verified:")
        print("   • Modern gr.ChatInterface implementation")
        print("   • Multi-modal file support (PDF, images)")
        print("   • Intelligent file type detection and routing")
        print("   • Orchestrator integration maintained")
        print("   • File processing feedback system")
        print("   • Error handling and graceful degradation")
        print("   • Backward compatibility with all agents")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()

from langchain.agents import Tool, Agent<PERSON><PERSON>cutor, create_react_agent
from langchain_ollama import OllamaLLM, OllamaEmbeddings
from langchain.prompts import PromptTemplate
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_community.vectorstores import Chroma
from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_community.chat_message_histories import Chat<PERSON>essageHistory
from duckduckgo_search import DDGS
from youtubesearchpython import VideosSearch
import requests
import base64
from io import BytesIO
from PIL import Image

class TextAgent:
    def __init__(self):
        self.llm = OllamaLLM(model="qwen3:0.6b", temperature=0.7)
        self.embeddings = OllamaEmbeddings(model="qwen3:0.6b")
        self.vectorstore = None
        self.chat_history = ChatMessageHistory()
        self.tools = self._setup_tools()
        self.agent = self._create_agent()
    
    def _setup_tools(self):
        search_tool = Tool(
            name="web_search",
            description="Search the internet for current information",
            func=self._web_search
        )
        
        youtube_tool = Tool(
            name="youtube_search",
            description="Search YouTube for videos on a topic",
            func=self._youtube_search
        )
        
        rag_tool = Tool(
            name="knowledge_base",
            description="Search internal knowledge base for relevant information",
            func=self._rag_search
        )
        
        return [search_tool, youtube_tool, rag_tool]
    
    def _web_search(self, query: str) -> str:
        try:
            with DDGS() as ddgs:
                results = list(ddgs.text(query, max_results=3))
                formatted_results = []
                for result in results:
                    formatted_results.append(f"Title: {result['title']}\nSnippet: {result['body']}\nURL: {result['href']}")
                return "\n\n".join(formatted_results)
        except Exception as e:
            return f"Search error: {str(e)}"
    
    def _youtube_search(self, query: str) -> str:
        try:
            videos_search = VideosSearch(query, limit=3)
            results = videos_search.result()['result']
            formatted_results = []
            for video in results:
                formatted_results.append(f"Title: {video['title']}\nChannel: {video['channel']['name']}\nURL: {video['link']}")
            return "\n\n".join(formatted_results)
        except Exception as e:
            return f"YouTube search error: {str(e)}"
    
    def _rag_search(self, query: str) -> str:
        if not self.vectorstore:
            return "No knowledge base available. Please upload documents first."
        
        try:
            docs = self.vectorstore.similarity_search(query, k=3)
            return "\n\n".join([doc.page_content for doc in docs])
        except Exception as e:
            return f"Knowledge base search error: {str(e)}"
    
    def _create_agent(self):
        template = """You are a helpful AI assistant with access to web search, YouTube search, and a knowledge base.
        Use these tools to provide comprehensive and accurate answers.
        
        Tools available:
        {tools}
        
        Use the following format:
        Question: the input question you must answer
        Thought: you should always think about what to do
        Action: the action to take, should be one of [{tool_names}]
        Action Input: the input to the action
        Observation: the result of the action
        ... (this Thought/Action/Action Input/Observation can repeat N times)
        Thought: I now know the final answer
        Final Answer: the final answer to the original input question
        
        Question: {input}
        Thought: {agent_scratchpad}"""
        
        prompt = PromptTemplate.from_template(template)
        agent = create_react_agent(self.llm, self.tools, prompt)
        return AgentExecutor(agent=agent, tools=self.tools, verbose=True, handle_parsing_errors=True)
    
    def add_documents_to_vectorstore(self, documents):
        if not self.vectorstore:
            self.vectorstore = Chroma.from_documents(documents, self.embeddings)
        else:
            self.vectorstore.add_documents(documents)
    
    def query(self, question: str) -> str:
        try:
            response = self.agent.invoke({"input": question})
            return response["output"]
        except Exception as e:
            return f"Error processing query: {str(e)}"

class ImageAgent:
    def __init__(self):
        self.llm = OllamaLLM(model="gemma3:4b", temperature=0.7)  # Use llava for vision
    
    def encode_image(self, image_path: str) -> str:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def analyze_image(self, image_path: str, query: str = "Describe this image in detail") -> str:
        try:
            # Simple approach for Ollama vision models
            response = self.llm.invoke(f"{query}\n[Image: {image_path}]")
            return response
        except Exception as e:
            return f"🖼️ Image analysis error: I can see the image but cannot process it properly. Please ensure the gemma3:4b model is installed with 'ollama pull gemma3:4b'. Error details: {str(e)}"
    
    def compare_images(self, image1_path: str, image2_path: str) -> str:
        try:
            query = "Compare these two images and describe their similarities and differences."
            response = self.llm.invoke(f"{query}\n[Image 1: {image1_path}]\n[Image 2: {image2_path}]")
            return response
        except Exception as e:
            return f"🔍 Image comparison error: {str(e)}"

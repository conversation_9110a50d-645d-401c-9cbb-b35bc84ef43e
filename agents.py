from langchain.agents import Tool, AgentExecutor, create_react_agent
from langchain_ollama import OllamaLLM, OllamaEmbeddings
from langchain.prompts import PromptTemplate
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_community.vectorstores import Chroma
from langchain_core.chat_history import Base<PERSON>hat<PERSON>essageHistory
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_community.chat_message_histories import Chat<PERSON>essageHistory
from duckduckgo_search import DDGS
from youtubesearchpython import VideosSearch
import requests
import base64
from io import BytesIO
from PIL import Image
import re
import json
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentType(Enum):
    """Enumeration of available agent types"""
    TEXT_RAG = "text_rag"
    IMAGE = "image"
    WEB_SEARCH = "web_search"
    YOUTUBE = "youtube"

class QueryIntent(Enum):
    """Enumeration of query intent types"""
    IMAGE_ANALYSIS = "image_analysis"
    KNOWLEDGE_BASE = "knowledge_base"
    WEB_SEARCH = "web_search"
    YOUTUBE_SEARCH = "youtube_search"
    GENERAL_CHAT = "general_chat"
    MULTI_MODAL = "multi_modal"

class TextAgent:
    def __init__(self):
        self.llm = OllamaLLM(model="qwen3:0.6b", temperature=0.7)
        self.embeddings = OllamaEmbeddings(model="qwen3:0.6b")
        self.vectorstore = None
        self.chat_history = ChatMessageHistory()
        self.tools = self._setup_tools()
        self.agent = self._create_agent()
        self.agent_type = AgentType.TEXT_RAG
        self.capabilities = [
            "web_search", "youtube_search", "knowledge_base_search",
            "document_analysis", "general_conversation"
        ]

    def _setup_tools(self):
        search_tool = Tool(
            name="web_search",
            description="Search the internet for current information",
            func=self._web_search
        )

        youtube_tool = Tool(
            name="youtube_search",
            description="Search YouTube for videos on a topic",
            func=self._youtube_search
        )

        rag_tool = Tool(
            name="knowledge_base",
            description="Search internal knowledge base for relevant information",
            func=self._rag_search
        )

        return [search_tool, youtube_tool, rag_tool]

    def _web_search(self, query: str) -> str:
        try:
            with DDGS() as ddgs:
                results = list(ddgs.text(query, max_results=3))
                formatted_results = []
                for result in results:
                    formatted_results.append(f"Title: {result['title']}\nSnippet: {result['body']}\nURL: {result['href']}")
                return "\n\n".join(formatted_results)
        except Exception as e:
            return f"Search error: {str(e)}"

    def _youtube_search(self, query: str) -> str:
        try:
            videos_search = VideosSearch(query, limit=3)
            results = videos_search.result()['result']
            formatted_results = []
            for video in results:
                formatted_results.append(f"Title: {video['title']}\nChannel: {video['channel']['name']}\nURL: {video['link']}")
            return "\n\n".join(formatted_results)
        except Exception as e:
            return f"YouTube search error: {str(e)}"

    def _rag_search(self, query: str) -> str:
        if not self.vectorstore:
            return "No knowledge base available. Please upload documents first."

        try:
            docs = self.vectorstore.similarity_search(query, k=3)
            return "\n\n".join([doc.page_content for doc in docs])
        except Exception as e:
            return f"Knowledge base search error: {str(e)}"

    def _create_agent(self):
        template = """You are a helpful AI assistant with access to web search, YouTube search, and a knowledge base.
        Use these tools to provide comprehensive and accurate answers.

        Tools available:
        {tools}

        Use the following format:
        Question: the input question you must answer
        Thought: you should always think about what to do
        Action: the action to take, should be one of [{tool_names}]
        Action Input: the input to the action
        Observation: the result of the action
        ... (this Thought/Action/Action Input/Observation can repeat N times)
        Thought: I now know the final answer
        Final Answer: the final answer to the original input question

        Question: {input}
        Thought: {agent_scratchpad}"""

        prompt = PromptTemplate.from_template(template)
        agent = create_react_agent(self.llm, self.tools, prompt)
        return AgentExecutor(agent=agent, tools=self.tools, verbose=True, handle_parsing_errors=True)

    def add_documents_to_vectorstore(self, documents):
        if not self.vectorstore:
            self.vectorstore = Chroma.from_documents(documents, self.embeddings)
        else:
            self.vectorstore.add_documents(documents)

    def query(self, question: str) -> Dict[str, Any]:
        """Enhanced query method that returns structured response with metadata"""
        try:
            logger.info(f"TextAgent processing query: {question[:100]}...")
            response = self.agent.invoke({"input": question})

            return {
                "success": True,
                "response": response["output"],
                "agent_type": self.agent_type.value,
                "tools_used": self._extract_tools_used(response.get("intermediate_steps", [])),
                "error": None
            }
        except Exception as e:
            logger.error(f"TextAgent error: {str(e)}")
            return {
                "success": False,
                "response": f"Error processing query: {str(e)}",
                "agent_type": self.agent_type.value,
                "tools_used": [],
                "error": str(e)
            }

    def _extract_tools_used(self, intermediate_steps: List) -> List[str]:
        """Extract tools used from intermediate steps"""
        tools_used = []
        for step in intermediate_steps:
            if hasattr(step, 'tool') and step.tool:
                tools_used.append(step.tool)
            elif isinstance(step, tuple) and len(step) > 0:
                # Handle different step formats
                if hasattr(step[0], 'tool'):
                    tools_used.append(step[0].tool)
        return list(set(tools_used))  # Remove duplicates

class ImageAgent:
    def __init__(self):
        self.llm = OllamaLLM(model="qwen2.5vl:3b", temperature=0.7)  # Use llava for vision
        self.agent_type = AgentType.IMAGE
        self.capabilities = [
            "image_analysis", "image_description", "visual_question_answering",
            "image_comparison", "text_extraction_from_images"
        ]

    def encode_image(self, image_path: str) -> str:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def analyze_image(self, image_path: str, query: str = "Describe this image in detail") -> Dict[str, Any]:
        """Enhanced image analysis method that returns structured response with metadata"""
        try:
            logger.info(f"ImageAgent analyzing image: {image_path} with query: {query[:100]}...")
            # Simple approach for Ollama vision models
            response = self.llm.invoke(f"{query}\n[Image: {image_path}]")

            return {
                "success": True,
                "response": response,
                "agent_type": self.agent_type.value,
                "image_path": image_path,
                "query": query,
                "error": None
            }
        except Exception as e:
            logger.error(f"ImageAgent error: {str(e)}")
            error_msg = f"🖼️ Image analysis error: I can see the image but cannot process it properly. Please ensure the qwen2.5vl:3b model is installed with 'ollama pull qwen2.5vl:3b'. Error details: {str(e)}"
            return {
                "success": False,
                "response": error_msg,
                "agent_type": self.agent_type.value,
                "image_path": image_path,
                "query": query,
                "error": str(e)
            }

    def compare_images(self, image1_path: str, image2_path: str) -> str:
        try:
            query = "Compare these two images and describe their similarities and differences."
            response = self.llm.invoke(f"{query}\n[Image 1: {image1_path}]\n[Image 2: {image2_path}]")
            return response
        except Exception as e:
            return f"🔍 Image comparison error: {str(e)}"


class OrchestratorAgent:
    """
    Central orchestrator agent that manages and coordinates all specialized agents.
    Acts as a dispatcher, query analyzer, and response coordinator.
    """

    def __init__(self, text_agent: TextAgent, image_agent: ImageAgent):
        self.text_agent = text_agent
        self.image_agent = image_agent
        self.llm = OllamaLLM(model="qwen3:0.6b", temperature=0.3)  # Lower temperature for routing decisions

        # Query analysis patterns
        self.web_search_keywords = [
            "latest", "recent", "current", "news", "today", "now", "update",
            "what's happening", "search for", "find information", "look up"
        ]

        self.youtube_keywords = [
            "video", "youtube", "watch", "tutorial", "how to", "show me",
            "demonstration", "clip", "movie", "film"
        ]

        self.knowledge_base_keywords = [
            "document", "uploaded", "file", "knowledge base", "my documents",
            "from the files", "based on", "according to", "summarize", "analyze document"
        ]

        logger.info("OrchestratorAgent initialized successfully")

    def analyze_query_intent(self, message: str, has_image: bool = False) -> QueryIntent:
        """
        Analyze user query to determine intent and appropriate agent routing
        """
        if not message:
            message = ""

        message_lower = message.lower()

        # Image analysis takes priority if image is present
        if has_image:
            if any(keyword in message_lower for keyword in ["compare", "difference", "similar"]):
                return QueryIntent.MULTI_MODAL  # Might need multiple agents
            return QueryIntent.IMAGE_ANALYSIS

        # Check for specific search intents
        if any(keyword in message_lower for keyword in self.youtube_keywords):
            return QueryIntent.YOUTUBE_SEARCH

        if any(keyword in message_lower for keyword in self.web_search_keywords):
            return QueryIntent.WEB_SEARCH

        if any(keyword in message_lower for keyword in self.knowledge_base_keywords):
            return QueryIntent.KNOWLEDGE_BASE

        # Use LLM for more complex intent detection
        try:
            intent_prompt = f"""
            Analyze this user query and determine the most appropriate intent:
            Query: "{message}"

            Available intents:
            1. WEB_SEARCH - for current information, news, real-time data
            2. YOUTUBE_SEARCH - for videos, tutorials, demonstrations
            3. KNOWLEDGE_BASE - for questions about uploaded documents or stored knowledge
            4. GENERAL_CHAT - for general conversation, explanations, or unclear intent

            Respond with only the intent name (e.g., "WEB_SEARCH").
            """

            response = self.llm.invoke(intent_prompt).strip().upper()

            # Map response to enum
            intent_mapping = {
                "WEB_SEARCH": QueryIntent.WEB_SEARCH,
                "YOUTUBE_SEARCH": QueryIntent.YOUTUBE_SEARCH,
                "KNOWLEDGE_BASE": QueryIntent.KNOWLEDGE_BASE,
                "GENERAL_CHAT": QueryIntent.GENERAL_CHAT
            }

            return intent_mapping.get(response, QueryIntent.GENERAL_CHAT)

        except Exception as e:
            logger.warning(f"Intent analysis failed, defaulting to GENERAL_CHAT: {e}")
            return QueryIntent.GENERAL_CHAT

    def route_query(self, message: str, image_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Main orchestration method that routes queries to appropriate agents
        and coordinates responses
        """
        try:
            has_image = image_path is not None
            intent = self.analyze_query_intent(message, has_image)

            logger.info(f"Query intent detected: {intent.value}")

            # Route based on intent
            if intent == QueryIntent.IMAGE_ANALYSIS:
                return self._handle_image_analysis(message, image_path)

            elif intent == QueryIntent.MULTI_MODAL:
                return self._handle_multi_modal_query(message, image_path)

            elif intent in [QueryIntent.WEB_SEARCH, QueryIntent.YOUTUBE_SEARCH,
                          QueryIntent.KNOWLEDGE_BASE, QueryIntent.GENERAL_CHAT]:
                return self._handle_text_query(message, intent)

            else:
                # Fallback to general chat
                return self._handle_text_query(message, QueryIntent.GENERAL_CHAT)

        except Exception as e:
            logger.error(f"Orchestrator routing error: {e}")
            return self._create_error_response(f"Orchestration error: {str(e)}")

    def _handle_image_analysis(self, message: str, image_path: str) -> Dict[str, Any]:
        """Handle image analysis queries"""
        try:
            query = message if message.strip() else "Describe this image in detail"
            result = self.image_agent.analyze_image(image_path, query)

            if result["success"]:
                return {
                    "success": True,
                    "response": result["response"],
                    "agents_used": [result["agent_type"]],
                    "intent": QueryIntent.IMAGE_ANALYSIS.value,
                    "metadata": {
                        "image_processed": True,
                        "query": query
                    }
                }
            else:
                # Fallback: try to provide helpful response even if image analysis fails
                fallback_response = self._get_image_analysis_fallback(message)
                return {
                    "success": False,
                    "response": f"{result['response']}\n\n{fallback_response}",
                    "agents_used": [result["agent_type"]],
                    "intent": QueryIntent.IMAGE_ANALYSIS.value,
                    "metadata": {
                        "image_processed": False,
                        "fallback_used": True,
                        "error": result["error"]
                    }
                }

        except Exception as e:
            logger.error(f"Image analysis handling error: {e}")
            return self._create_error_response(f"Image analysis error: {str(e)}")

    def _handle_text_query(self, message: str, intent: QueryIntent) -> Dict[str, Any]:
        """Handle text-based queries through TextAgent"""
        try:
            # Add intent context to the query for better agent performance
            enhanced_message = self._enhance_message_with_intent(message, intent)
            result = self.text_agent.query(enhanced_message)

            if result["success"]:
                return {
                    "success": True,
                    "response": result["response"],
                    "agents_used": [result["agent_type"]],
                    "intent": intent.value,
                    "metadata": {
                        "tools_used": result["tools_used"],
                        "enhanced_query": enhanced_message != message
                    }
                }
            else:
                # Implement retry logic for failed queries
                retry_result = self._retry_text_query(message, intent)
                return retry_result

        except Exception as e:
            logger.error(f"Text query handling error: {e}")
            return self._create_error_response(f"Text query error: {str(e)}")

    def _handle_multi_modal_query(self, message: str, image_path: str) -> Dict[str, Any]:
        """Handle queries that require both image and text analysis"""
        try:
            # First, analyze the image
            image_result = self.image_agent.analyze_image(image_path, message)

            # Then, use the image analysis result to enhance the text query
            if image_result["success"]:
                enhanced_message = f"{message}\n\nImage analysis context: {image_result['response']}"
                text_result = self.text_agent.query(enhanced_message)

                # Combine responses
                combined_response = self._combine_responses([image_result, text_result])

                return {
                    "success": True,
                    "response": combined_response,
                    "agents_used": [image_result["agent_type"], text_result.get("agent_type", "text_rag")],
                    "intent": QueryIntent.MULTI_MODAL.value,
                    "metadata": {
                        "image_processed": True,
                        "text_processed": text_result.get("success", False),
                        "tools_used": text_result.get("tools_used", [])
                    }
                }
            else:
                # Fallback to text-only if image analysis fails
                return self._handle_text_query(message, QueryIntent.GENERAL_CHAT)

        except Exception as e:
            logger.error(f"Multi-modal query handling error: {e}")
            return self._create_error_response(f"Multi-modal query error: {str(e)}")

    def _enhance_message_with_intent(self, message: str, intent: QueryIntent) -> str:
        """Enhance message with intent-specific context for better agent performance"""
        intent_prefixes = {
            QueryIntent.WEB_SEARCH: "Search the web for current information about: ",
            QueryIntent.YOUTUBE_SEARCH: "Find YouTube videos about: ",
            QueryIntent.KNOWLEDGE_BASE: "Search the knowledge base for information about: ",
            QueryIntent.GENERAL_CHAT: ""  # No prefix for general chat
        }

        prefix = intent_prefixes.get(intent, "")
        return f"{prefix}{message}" if prefix else message

    def _retry_text_query(self, message: str, intent: QueryIntent, max_retries: int = 2) -> Dict[str, Any]:
        """Implement retry logic with fallback strategies"""
        for attempt in range(max_retries):
            try:
                # Try with simplified message
                simplified_message = self._simplify_message(message)
                result = self.text_agent.query(simplified_message)

                if result["success"]:
                    return {
                        "success": True,
                        "response": result["response"],
                        "agents_used": [result["agent_type"]],
                        "intent": intent.value,
                        "metadata": {
                            "retry_attempt": attempt + 1,
                            "simplified_query": True,
                            "tools_used": result["tools_used"]
                        }
                    }

            except Exception as e:
                logger.warning(f"Retry attempt {attempt + 1} failed: {e}")
                continue

        # Final fallback
        return {
            "success": False,
            "response": "I apologize, but I'm having trouble processing your request right now. Please try rephrasing your question or try again later.",
            "agents_used": ["orchestrator"],
            "intent": intent.value,
            "metadata": {
                "max_retries_exceeded": True,
                "fallback_response": True
            }
        }

    def _simplify_message(self, message: str) -> str:
        """Simplify complex messages for retry attempts"""
        # Remove special characters and extra whitespace
        simplified = re.sub(r'[^\w\s]', ' ', message)
        simplified = ' '.join(simplified.split())
        return simplified[:200]  # Truncate if too long

    def _combine_responses(self, results: List[Dict[str, Any]]) -> str:
        """Combine responses from multiple agents into a coherent answer"""
        successful_responses = [r["response"] for r in results if r.get("success", False)]

        if len(successful_responses) == 1:
            return successful_responses[0]
        elif len(successful_responses) > 1:
            return f"Based on my analysis:\n\n🖼️ **Image Analysis:**\n{successful_responses[0]}\n\n🔍 **Additional Information:**\n{successful_responses[1]}"
        else:
            return "I encountered difficulties processing your request with multiple agents."

    def _get_image_analysis_fallback(self, message: str) -> str:
        """Provide fallback response when image analysis fails"""
        return "💡 **Tip:** While I couldn't analyze the image, I can still help with text-based questions. Try describing what you see in the image, and I can provide relevant information."

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "success": False,
            "response": f"I apologize, but I encountered an error: {error_message}",
            "agents_used": ["orchestrator"],
            "intent": "error",
            "metadata": {
                "error": True,
                "error_message": error_message
            }
        }

    def get_agent_status(self) -> Dict[str, Any]:
        """Get status information about all managed agents"""
        return {
            "orchestrator": {
                "status": "active",
                "capabilities": ["query_routing", "intent_analysis", "response_coordination"]
            },
            "text_agent": {
                "status": "active",
                "capabilities": self.text_agent.capabilities,
                "has_vectorstore": self.text_agent.vectorstore is not None
            },
            "image_agent": {
                "status": "active",
                "capabilities": self.image_agent.capabilities
            }
        }

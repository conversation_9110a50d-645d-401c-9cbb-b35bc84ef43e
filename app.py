import gradio as gr
import os
import tempfile
from agents import Text<PERSON>gent, ImageAgent, OrchestratorAgent
from document_processor import DocumentProcessor

class MultiAgentRAGApp:
    def __init__(self):
        print("🔧 Initializing specialized agents...")
        self.text_agent = TextAgent()
        self.image_agent = ImageAgent()
        self.doc_processor = DocumentProcessor()

        print("🎯 Initializing central orchestrator...")
        self.orchestrator = OrchestratorAgent(self.text_agent, self.image_agent)

        self.chat_history = []
        print("✅ Multi-Agent RAG system initialized successfully!")

    def upload_documents(self, files):
        if not files:
            return "No files uploaded."

        try:
            file_paths = [file.name for file in files]
            documents = self.doc_processor.process_uploaded_files(file_paths)
            self.text_agent.add_documents_to_vectorstore(documents)
            return f"✅ Successfully processed {len(documents)} document chunks from {len(files)} files."
        except Exception as e:
            return f"❌ Error processing documents: {str(e)}"

    def add_text_to_knowledge_base(self, text):
        if not text.strip():
            return "No text provided."

        try:
            documents = self.doc_processor.process_text_input(text)
            self.text_agent.add_documents_to_vectorstore(documents)
            return f"✅ Successfully added text to knowledge base ({len(documents)} chunks)."
        except Exception as e:
            return f"❌ Error adding text: {str(e)}"

    def process_message(self, message, image, history):
        if not message.strip() and image is None:
            return history, "", None

        try:
            # Prepare image path if image is provided
            image_path = None
            if image is not None:
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
                image.save(temp_file.name)
                image_path = temp_file.name
                temp_file.close()

            # Use orchestrator to route and process the query
            display_message = message if message.strip() else "Analyze this image"
            result = self.orchestrator.route_query(message, image_path)

            # Clean up temporary file
            if image_path and os.path.exists(image_path):
                os.unlink(image_path)

            # Extract response and add metadata info if available
            response = result["response"]

            # Add orchestrator metadata to response for debugging (optional)
            if result.get("metadata") and result["metadata"].get("tools_used"):
                tools_info = ", ".join(result["metadata"]["tools_used"])
                print(f"🔧 Tools used: {tools_info}")

            if result.get("agents_used"):
                agents_info = ", ".join(result["agents_used"])
                print(f"🤖 Agents used: {agents_info}")

            # Add to chat history in messages format
            history.append({"role": "user", "content": display_message})
            history.append({"role": "assistant", "content": response})
            return history, "", None

        except Exception as e:
            # Clean up temporary file in case of error
            if 'image_path' in locals() and image_path and os.path.exists(image_path):
                os.unlink(image_path)

            error_msg = f"I apologize, but I encountered an error while processing your request: {str(e)}"
            history.append({"role": "user", "content": message if message.strip() else "Image upload"})
            history.append({"role": "assistant", "content": error_msg})
            return history, "", None

def create_interface():
    app = MultiAgentRAGApp()

    # Custom CSS for ChatGPT-like appearance
    css = """
    .main-container {
        max-width: 1200px;
        margin: 0 auto;
        background: #ffffff;
    }

    .chat-container {
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .input-row {
        background: #f9f9f9;
        border-radius: 12px;
        padding: 8px;
        margin: 16px 0;
        border: 1px solid #e1e5e9;
    }

    .send-button {
        background: #10a37f !important;
        border: none !important;
        border-radius: 8px !important;
        color: white !important;
        font-weight: 600 !important;
    }

    .send-button:hover {
        background: #0d8168 !important;
    }

    .sidebar {
        background: #f7f7f8;
        border-radius: 8px;
        padding: 16px;
        margin-left: 16px;
    }

    .sidebar-section {
        background: white;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
        border: 1px solid #e1e5e9;
    }

    .title-header {
        text-align: center;
        color: #2d3748;
        margin-bottom: 24px;
    }

    .capabilities-list {
        font-size: 14px;
        color: #4a5568;
        line-height: 1.6;
    }
    """

    with gr.Blocks(
        title="Multi-Agent RAG Assistant",
        theme=gr.themes.Soft(primary_hue="emerald"),
        css=css
    ) as interface:

        with gr.Column(elem_classes=["main-container"]):
            # Header
            gr.Markdown(
                """
                # 🤖 Multi-Agent RAG Assistant
                ### Intelligent orchestrator with specialized agents for web search, document analysis, and image understanding
                #### 🎯 **New:** Central orchestrator automatically routes your queries to the best specialized agent
                """,
                elem_classes=["title-header"]
            )

            with gr.Row():
                # Main Chat Area
                with gr.Column(scale=3, elem_classes=["chat-container"]):

                    # Chat Display
                    chatbot = gr.Chatbot(
                        height=600,
                        type="messages",
                        show_label=False,
                        avatar_images=("https://cdn-icons-png.flaticon.com/512/1077/1077114.png", "https://cdn-icons-png.flaticon.com/512/4712/4712139.png"),
                        show_share_button=False,
                        show_copy_button=True
                    )

                    # Input Area
                    with gr.Row(elem_classes=["input-row"]):
                        with gr.Column(scale=1, min_width=80):
                            image_input = gr.Image(
                                type="pil",
                                show_label=False,
                                height=60,
                                width=60,
                                container=False,
                                show_download_button=False
                            )

                        with gr.Column(scale=6):
                            msg = gr.Textbox(
                                placeholder="Message Multi-Agent RAG Assistant...",
                                show_label=False,
                                lines=1,
                                max_lines=8,
                                container=False,
                                scale=1
                            )

                        with gr.Column(scale=1, min_width=80):
                            send_btn = gr.Button(
                                "Send",
                                variant="primary",
                                elem_classes=["send-button"],
                                size="sm"
                            )

                    # Action Buttons
                    with gr.Row():
                        clear_btn = gr.Button(
                            "🗑️ Clear Chat",
                            variant="secondary",
                            size="sm"
                        )
                        example_btn = gr.Button(
                            "💡 Show Examples",
                            variant="secondary",
                            size="sm"
                        )

                # Sidebar
                with gr.Column(scale=1, elem_classes=["sidebar"]):

                    # Knowledge Base Section
                    with gr.Group(elem_classes=["sidebar-section"]):
                        gr.Markdown("### 📚 Knowledge Base")

                        file_upload = gr.File(
                            file_count="multiple",
                            file_types=[".txt", ".pdf"],
                            label="Upload Documents",
                            height=80
                        )

                        upload_btn = gr.Button(
                            "📤 Process",
                            variant="secondary",
                            size="sm",
                            scale=1
                        )

                        upload_status = gr.Textbox(
                            show_label=False,
                            placeholder="Upload status will appear here...",
                            interactive=False,
                            max_lines=2
                        )


        # Examples for new users
        examples_display = gr.Markdown(
            """
            ## 💡 Example Prompts - Orchestrator Intelligence

            **🌐 Web Search (Auto-detected):**
            - "What's the latest news about AI?"
            - "Current weather in New York"
            - "Recent developments in quantum computing"

            **📺 YouTube Search (Auto-detected):**
            - "Find YouTube videos about machine learning"
            - "Show me tutorials on Python programming"
            - "Watch demonstrations of robotics"

            **📚 Knowledge Base (Auto-detected):**
            - "Summarize the uploaded document"
            - "What are the key points in my files?"
            - "Based on my documents, what is..."

            **🖼️ Image Analysis (Auto-detected):**
            - Upload an image and ask "What do you see?"
            - "Explain the text in this image"
            - "Compare this image with similar concepts"

            **🔄 Multi-Modal Queries:**
            - Upload an image + "Find similar images online"
            - Upload a chart + "Explain this data and find related research"

            **✨ The orchestrator automatically determines which agent(s) to use!**
            """,
            visible=False
        )

        # Event Handlers
        def process_chat(message, image, history):
            return app.process_message(message, image, history)

        def show_examples():
            return gr.update(visible=True)

        def hide_examples():
            return gr.update(visible=False)

        # Chat functionality
        send_btn.click(
            process_chat,
            inputs=[msg, image_input, chatbot],
            outputs=[chatbot, msg, image_input]
        )

        msg.submit(
            process_chat,
            inputs=[msg, image_input, chatbot],
            outputs=[chatbot, msg, image_input]
        )

        clear_btn.click(
            lambda: ([], "", None),
            outputs=[chatbot, msg, image_input]
        )

        # Knowledge base functionality
        upload_btn.click(
            app.upload_documents,
            inputs=[file_upload],
            outputs=[upload_status]
        )


        # Examples
        example_btn.click(
            show_examples,
            outputs=[examples_display]
        )

        chatbot.change(
            hide_examples,
            outputs=[examples_display]
        )

    return interface

if __name__ == "__main__":
    print("🚀 Starting Multi-Agent RAG Assistant with Central Orchestrator...")
    print("🎯 Orchestrator will intelligently route queries to specialized agents")
    print("🎨 ChatGPT-like interface loading...")
    print("📋 Required models: qwen3:0.6b, qwen2.5vl:3b")
    print("🌐 Available at: http://localhost:7863")
    print("👤 Avatar images: User and Assistant icons loaded")

    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False,
        show_error=True
    )
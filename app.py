import gradio as gr
import os
import tempfile
from agents import TextAgent, ImageAgent
from document_processor import DocumentProcessor

class MultiAgentRAGApp:
    def __init__(self):
        self.text_agent = TextAgent()
        self.image_agent = ImageAgent()
        self.doc_processor = DocumentProcessor()
        self.chat_history = []
    
    def upload_documents(self, files):
        if not files:
            return "No files uploaded."
        
        try:
            file_paths = [file.name for file in files]
            documents = self.doc_processor.process_uploaded_files(file_paths)
            self.text_agent.add_documents_to_vectorstore(documents)
            return f"✅ Successfully processed {len(documents)} document chunks from {len(files)} files."
        except Exception as e:
            return f"❌ Error processing documents: {str(e)}"
    
    def add_text_to_knowledge_base(self, text):
        if not text.strip():
            return "No text provided."
        
        try:
            documents = self.doc_processor.process_text_input(text)
            self.text_agent.add_documents_to_vectorstore(documents)
            return f"✅ Successfully added text to knowledge base ({len(documents)} chunks)."
        except Exception as e:
            return f"❌ Error adding text: {str(e)}"
    
    def process_message(self, message, image, history):
        if not message.strip() and image is None:
            return history, "", None
        
        try:
            if image is not None:
                # Handle image analysis
                with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                    image.save(temp_file.name)
                    if message.strip():
                        response = self.image_agent.analyze_image(temp_file.name, message)
                        display_message = message
                    else:
                        response = self.image_agent.analyze_image(temp_file.name, "Describe this image in detail")
                        display_message = "Analyze this image"
                    os.unlink(temp_file.name)
            else:
                # Handle text-only query
                response = self.text_agent.query(message)
                display_message = message
            
            # Add to chat history in messages format
            history.append({"role": "user", "content": display_message})
            history.append({"role": "assistant", "content": response})
            return history, "", None
        except Exception as e:
            error_msg = f"I apologize, but I encountered an error while processing your request: {str(e)}"
            history.append({"role": "user", "content": message if message.strip() else "Image upload"})
            history.append({"role": "assistant", "content": error_msg})
            return history, "", None

def create_interface():
    app = MultiAgentRAGApp()
    
    # Custom CSS for ChatGPT-like appearance
    css = """
    .main-container {
        max-width: 1200px;
        margin: 0 auto;
        background: #ffffff;
    }
    
    .chat-container {
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .input-row {
        background: #f9f9f9;
        border-radius: 12px;
        padding: 8px;
        margin: 16px 0;
        border: 1px solid #e1e5e9;
    }
    
    .send-button {
        background: #10a37f !important;
        border: none !important;
        border-radius: 8px !important;
        color: white !important;
        font-weight: 600 !important;
    }
    
    .send-button:hover {
        background: #0d8168 !important;
    }
    
    .sidebar {
        background: #f7f7f8;
        border-radius: 8px;
        padding: 16px;
        margin-left: 16px;
    }
    
    .sidebar-section {
        background: white;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
        border: 1px solid #e1e5e9;
    }
    
    .title-header {
        text-align: center;
        color: #2d3748;
        margin-bottom: 24px;
    }
    
    .capabilities-list {
        font-size: 14px;
        color: #4a5568;
        line-height: 1.6;
    }
    """
    
    with gr.Blocks(
        title="Multi-Agent RAG Assistant", 
        theme=gr.themes.Soft(primary_hue="emerald"),
        css=css
    ) as interface:
        
        with gr.Column(elem_classes=["main-container"]):
            # Header
            gr.Markdown(
                """
                # 🤖 Multi-Agent RAG Assistant
                ### Your AI companion with web search, document analysis, and image understanding
                """,
                elem_classes=["title-header"]
            )
            
            with gr.Row():
                # Main Chat Area
                with gr.Column(scale=3, elem_classes=["chat-container"]):
                    
                    # Chat Display
                    chatbot = gr.Chatbot(
                        height=600,
                        type="messages",
                        show_label=False,
                        avatar_images=("https://raw.githubusercontent.com/gradio-app/gradio/main/gradio/themes/files/logo.svg", "🤖"),
                        show_share_button=False,
                        show_copy_button=True
                    )
                    
                    # Input Area
                    with gr.Row(elem_classes=["input-row"]):
                        with gr.Column(scale=1, min_width=80):
                            image_input = gr.Image(
                                type="pil",
                                show_label=False,
                                height=60,
                                width=60,
                                container=False,
                                show_download_button=False
                            )
                        
                        with gr.Column(scale=6):
                            msg = gr.Textbox(
                                placeholder="Message Multi-Agent RAG Assistant...",
                                show_label=False,
                                lines=1,
                                max_lines=8,
                                container=False,
                                scale=1
                            )
                        
                        with gr.Column(scale=1, min_width=80):
                            send_btn = gr.Button(
                                "Send",
                                variant="primary",
                                elem_classes=["send-button"],
                                size="sm"
                            )
                    
                    # Action Buttons
                    with gr.Row():
                        clear_btn = gr.Button(
                            "🗑️ Clear Chat",
                            variant="secondary",
                            size="sm"
                        )
                        example_btn = gr.Button(
                            "💡 Show Examples",
                            variant="secondary",
                            size="sm"
                        )
                
                # Sidebar
                with gr.Column(scale=1, elem_classes=["sidebar"]):
                    
                    # Knowledge Base Section
                    with gr.Group(elem_classes=["sidebar-section"]):
                        gr.Markdown("### 📚 Knowledge Base")
                        
                        file_upload = gr.File(
                            file_count="multiple",
                            file_types=[".txt", ".pdf"],
                            label="Upload Documents",
                            height=80
                        )
                        
                        upload_btn = gr.Button(
                            "📤 Process",
                            variant="secondary",
                            size="sm",
                            scale=1
                        )
                        
                        upload_status = gr.Textbox(
                            show_label=False,
                            placeholder="Upload status will appear here...",
                            interactive=False,
                            max_lines=2
                        )
                    
        
        # Examples for new users
        examples_display = gr.Markdown(
            """
            ## 💡 Example Prompts
            
            **General Questions:**
            - "What's the latest news about AI?"
            - "Find YouTube videos about machine learning"
            
            **With Documents:**
            - "Summarize the uploaded document"
            - "What are the key points in my files?"
            
            **With Images:**
            - Upload an image and ask "What do you see?"
            - "Explain the text in this image"
            
            **Research:**
            - "Research the benefits of renewable energy"
            - "Find recent developments in quantum computing"
            """,
            visible=False
        )
        
        # Event Handlers
        def process_chat(message, image, history):
            return app.process_message(message, image, history)
        
        def show_examples():
            return gr.update(visible=True)
        
        def hide_examples():
            return gr.update(visible=False)
        
        # Chat functionality
        send_btn.click(
            process_chat,
            inputs=[msg, image_input, chatbot],
            outputs=[chatbot, msg, image_input]
        )
        
        msg.submit(
            process_chat,
            inputs=[msg, image_input, chatbot],
            outputs=[chatbot, msg, image_input]
        )
        
        clear_btn.click(
            lambda: ([], "", None),
            outputs=[chatbot, msg, image_input]
        )
        
        # Knowledge base functionality
        upload_btn.click(
            app.upload_documents,
            inputs=[file_upload],
            outputs=[upload_status]
        )
        
        
        # Examples
        example_btn.click(
            show_examples,
            outputs=[examples_display]
        )
        
        chatbot.change(
            hide_examples,
            outputs=[examples_display]
        )
    
    return interface

if __name__ == "__main__":
    print("🚀 Starting Multi-Agent RAG Assistant...")
    print("🎨 ChatGPT-like interface loading...")
    print("📋 Required models: qwen3:0.6b, gemma3:4b")
    print("🌐 Available at: http://localhost:7860")
    
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
import gradio as gr
from typing import List
from pathlib import Path
from agents import Text<PERSON>gent, ImageAgent, OrchestratorAgent
from document_processor import DocumentProcessor

class MultiAgentRAGApp:
    def __init__(self):
        print("🔧 Initializing specialized agents...")
        self.text_agent = TextAgent()
        self.image_agent = ImageAgent()
        self.doc_processor = DocumentProcessor()

        print("🎯 Initializing central orchestrator...")
        self.orchestrator = OrchestratorAgent(self.text_agent, self.image_agent)

        # Supported file types
        self.supported_image_types = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        self.supported_document_types = {'.pdf', '.txt', '.md', '.docx'}

        print("✅ Multi-Agent RAG system initialized successfully!")

    def detect_file_type(self, file_path: str) -> str:
        """Detect if file is image, document, or unsupported"""
        if not file_path:
            return "none"

        file_ext = Path(file_path).suffix.lower()

        if file_ext in self.supported_image_types:
            return "image"
        elif file_ext in self.supported_document_types:
            return "document"
        else:
            return "unsupported"

    def process_uploaded_file(self, file_path: str) -> str:
        """Process uploaded file based on its type"""
        if not file_path:
            return ""

        file_type = self.detect_file_type(file_path)
        file_name = Path(file_path).name

        try:
            if file_type == "document":
                # Process document for knowledge base
                documents = self.doc_processor.process_uploaded_files([file_path])
                self.text_agent.add_documents_to_vectorstore(documents)
                return f"📚 **Document Processed**: {file_name} added to knowledge base ({len(documents)} chunks)"

            elif file_type == "image":
                # Image will be processed when user asks questions about it
                return f"🖼️ **Image Uploaded**: {file_name} ready for visual question answering"

            elif file_type == "unsupported":
                return f"❌ **Unsupported File**: {file_name} - Please upload PDF, TXT, or image files (JPG, PNG)"

            else:
                return ""

        except Exception as e:
            return f"❌ **Error Processing {file_name}**: {str(e)}"

    def chat_function(self, message: str, history: List, files: List = None) -> str:
        """
        Main chat function for gr.ChatInterface

        Args:
            message: User's text message
            history: Chat history (handled by gr.ChatInterface)
            files: List of uploaded files

        Returns:
            Assistant's response
        """
        try:
            # Process any uploaded files first
            file_processing_messages = []
            image_path = None

            if files:
                for file in files:
                    file_path = file.name if hasattr(file, 'name') else str(file)
                    file_type = self.detect_file_type(file_path)

                    if file_type == "image":
                        # Store image path for visual question answering
                        image_path = file_path
                        file_processing_messages.append(self.process_uploaded_file(file_path))
                    elif file_type == "document":
                        # Process document immediately
                        file_processing_messages.append(self.process_uploaded_file(file_path))
                    else:
                        file_processing_messages.append(self.process_uploaded_file(file_path))

            # If no message but files were uploaded, provide file processing feedback
            if not message.strip() and files:
                if image_path:
                    message = "Analyze this image"
                else:
                    return "\n".join(file_processing_messages)

            # Use orchestrator to route and process the query
            result = self.orchestrator.route_query(message, image_path)

            # Extract response
            response = result["response"]

            # Add file processing messages if any
            if file_processing_messages:
                response = "\n".join(file_processing_messages) + "\n\n" + response

            # Log orchestrator metadata for debugging
            if result.get("metadata") and result["metadata"].get("tools_used"):
                tools_info = ", ".join(result["metadata"]["tools_used"])
                print(f"🔧 Tools used: {tools_info}")

            if result.get("agents_used"):
                agents_info = ", ".join(result["agents_used"])
                print(f"🤖 Agents used: {agents_info}")

            return response

        except Exception as e:
            error_msg = f"I apologize, but I encountered an error while processing your request: {str(e)}"
            print(f"❌ Error in chat_function: {e}")
            return error_msg

def create_interface():
    app = MultiAgentRAGApp()

    # Custom CSS for modern ChatGPT-like appearance
    css = """
    /* Main container styling */
    .gradio-container {
        max-width: 1200px !important;
        margin: 0 auto !important;
    }

    /* Chat interface styling */
    .chat-interface {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        background: #ffffff;
    }

    /* File upload area styling */
    .file-upload-area {
        border: 2px dashed #e1e5e9;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #f8f9fa;
        margin: 10px 0;
    }

    /* Message styling */
    .message {
        border-radius: 12px;
        padding: 12px 16px;
        margin: 8px 0;
    }

    /* Send button styling */
    .send-button {
        background: #10a37f !important;
        border: none !important;
        border-radius: 8px !important;
        color: white !important;
        font-weight: 600 !important;
        padding: 8px 16px !important;
    }

    .send-button:hover {
        background: #0d8168 !important;
    }

    /* Header styling */
    .header-section {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px 12px 0 0;
        margin-bottom: 20px;
    }

    /* Examples section */
    .examples-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        border-left: 4px solid #10a37f;
    }

    /* File type indicators */
    .file-indicator {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        margin: 2px;
    }

    .file-indicator.pdf {
        background: #ff6b6b;
        color: white;
    }

    .file-indicator.image {
        background: #4ecdc4;
        color: white;
    }
    """

    # Create the main interface using gr.ChatInterface
    with gr.Blocks(
        title="Multi-Agent RAG Assistant",
        theme=gr.themes.Soft(primary_hue="emerald"),
        css=css,
        fill_height=True
    ) as interface:

        # Header Section
        with gr.Row(elem_classes=["header-section"]):
            gr.Markdown(
                """
                # 🤖 Multi-Agent RAG Assistant
                ### Modern ChatInterface with Multi-Modal File Support
                **🎯 Intelligent orchestrator • 📚 Document processing • 🖼️ Visual Q&A • 🌐 Web search**
                """,
                elem_classes=["header-content"]
            )

        # File Upload Instructions
        with gr.Row(elem_classes=["examples-section"]):
            gr.Markdown(
                """
                ### 📁 **Multi-Modal File Support**

                **📚 Documents (PDF, TXT)**: Automatically added to knowledge base for RAG queries
                **🖼️ Images (JPG, PNG)**: Ready for visual question answering

                **💡 Usage**: Upload files and ask questions - the orchestrator will route to the right agent!
                """,
                elem_classes=["file-instructions"]
            )

        # Main Chat Interface using gr.ChatInterface
        gr.ChatInterface(
            fn=app.chat_function,
            title="",  # We have our own header
            description="",  # We have our own description
            examples=[
                ["What's the latest news about AI?", None],
                ["Find YouTube videos about machine learning", None],
                ["Summarize my uploaded documents", None],
                ["What do you see in this image?", None],
                ["Search for recent developments in quantum computing", None]
            ],
            additional_inputs=[
                gr.File(
                    file_count="multiple",
                    file_types=[".pdf", ".txt", ".md", ".docx", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"],
                    label="📁 Upload Files (Documents for RAG, Images for Visual Q&A)",
                    elem_classes=["file-upload-area"]
                )
            ],
            additional_inputs_accordion="📎 File Upload",
            chatbot=gr.Chatbot(
                height=500,
                type="messages",  # Fix the warning
                show_label=False,
                avatar_images=(
                    "https://cdn-icons-png.flaticon.com/512/1077/1077114.png",  # User avatar
                    "https://cdn-icons-png.flaticon.com/512/4712/4712027.png"   # Assistant avatar
                ),
                show_share_button=False,
                show_copy_button=True,
                elem_classes=["chat-interface"]
            ),
            textbox=gr.Textbox(
                placeholder="💬 Ask questions, upload documents for RAG, or upload images for visual Q&A...",
                container=False,
                scale=7
            ),
            submit_btn="Send"  # Simplified button specification
        )

        # Additional Information Section
        with gr.Row(elem_classes=["examples-section"]):
            gr.Markdown(
                """
                ### 💡 **How to Use the Multi-Agent RAG Assistant**

                **🌐 Web Search**: Ask about current events, news, or real-time information
                - *"What's the latest news about AI?"*
                - *"Current weather in New York"*

                **📺 YouTube Search**: Find videos, tutorials, or demonstrations
                - *"Find YouTube videos about machine learning"*
                - *"Show me Python programming tutorials"*

                **📚 Knowledge Base**: Upload documents and ask questions about them
                - *"Summarize my uploaded document"*
                - *"What are the key points in my files?"*

                **🖼️ Visual Q&A**: Upload images and ask questions about them
                - *"What do you see in this image?"*
                - *"Describe the colors and composition"*

                **🔄 Multi-Modal**: Combine files with questions for complex analysis
                - Upload chart + *"Explain this data and find related research"*
                - Upload image + *"Find similar images online"*

                **✨ The orchestrator automatically determines which agent(s) to use!**
                """,
                elem_classes=["usage-guide"]
            )

    return interface

if __name__ == "__main__":
    print("🚀 Starting Multi-Agent RAG Assistant with Modern ChatInterface...")
    print("🎯 Orchestrator will intelligently route queries to specialized agents")
    print("📁 Multi-modal file support: Documents (PDF, TXT) + Images (JPG, PNG)")
    print("🎨 Modern gr.ChatInterface with enhanced user experience")
    print("📋 Required models: qwen3:0.6b, qwen2.5vl:3b")
    print("🌐 Available at: http://localhost:7863")
    print("👤 Avatar images: User and Assistant icons loaded")

    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False,
        show_error=True
    )
"""
Agents package for the Multi-Agent RAG system.

This package provides a modular architecture for specialized AI agents that work together
to handle different types of queries and tasks. The package includes:

- BaseAgent: Abstract base class for all agents
- TextAgent: Handles text queries, web search, YouTube search, and RAG
- ImageAgent: Handles image analysis and visual question answering
- OrchestratorAgent: Central coordinator that routes queries to appropriate agents

Usage:
    from agents import TextAgent, ImageAgent, OrchestratorAgent
    
    # Initialize specialized agents
    text_agent = TextAgent()
    image_agent = ImageAgent()
    
    # Initialize orchestrator with specialized agents
    orchestrator = OrchestratorAgent(text_agent, image_agent)
    
    # Process queries through the orchestrator
    result = orchestrator.route_query("What's the latest news about AI?")
"""

from .base_agent import BaseAgent, AgentError, AgentInitializationError, AgentProcessingError
from .text_agent import TextAgent
from .image_agent import ImageAgent
from .orchestrator_agent import OrchestratorAgent

# Import configuration enums for backward compatibility
from config import AgentType, QueryIntent

# Version information
__version__ = "1.0.0"
__author__ = "Multi-Agent RAG Team"

# Public API
__all__ = [
    # Base classes
    "BaseAgent",
    "AgentError", 
    "AgentInitializationError",
    "AgentProcessingError",
    
    # Specialized agents
    "TextAgent",
    "ImageAgent", 
    "OrchestratorAgent",
    
    # Configuration enums
    "AgentType",
    "QueryIntent",
    
    # Package metadata
    "__version__",
    "__author__"
]


def create_orchestrator_system():
    """
    Factory function to create a complete orchestrator system with all agents.
    
    This is a convenience function that initializes all agents in the correct order
    and returns a ready-to-use orchestrator system.
    
    Returns:
        OrchestratorAgent: Fully initialized orchestrator with all specialized agents
        
    Example:
        >>> orchestrator = create_orchestrator_system()
        >>> result = orchestrator.route_query("Analyze this image", image_path="photo.jpg")
    """
    # Initialize specialized agents
    text_agent = TextAgent()
    image_agent = ImageAgent()
    
    # Create and return orchestrator
    orchestrator = OrchestratorAgent(text_agent, image_agent)
    
    return orchestrator


def get_available_agents():
    """
    Get information about all available agent types and their capabilities.
    
    Returns:
        Dict[str, Dict]: Dictionary mapping agent names to their information
        
    Example:
        >>> agents_info = get_available_agents()
        >>> print(agents_info["TextAgent"]["capabilities"])
    """
    from config import AgentCapabilities
    
    return {
        "TextAgent": {
            "class": TextAgent,
            "type": AgentType.TEXT_RAG,
            "capabilities": AgentCapabilities.TEXT_AGENT_CAPABILITIES,
            "description": "Handles text queries, web search, YouTube search, and knowledge base operations"
        },
        "ImageAgent": {
            "class": ImageAgent,
            "type": AgentType.IMAGE,
            "capabilities": AgentCapabilities.IMAGE_AGENT_CAPABILITIES,
            "description": "Handles image analysis, visual question answering, and image comparison"
        },
        "OrchestratorAgent": {
            "class": OrchestratorAgent,
            "type": AgentType.TEXT_RAG,  # Uses text model for routing
            "capabilities": AgentCapabilities.ORCHESTRATOR_CAPABILITIES,
            "description": "Central coordinator that routes queries to appropriate specialized agents"
        }
    }


def validate_agent_dependencies():
    """
    Validate that all required dependencies for agents are available.
    
    Returns:
        Dict[str, bool]: Dictionary indicating which dependencies are available
        
    Raises:
        ImportError: If critical dependencies are missing
    """
    dependencies = {}
    
    try:
        import langchain
        dependencies["langchain"] = True
    except ImportError:
        dependencies["langchain"] = False
    
    try:
        import langchain_ollama
        dependencies["langchain_ollama"] = True
    except ImportError:
        dependencies["langchain_ollama"] = False
    
    try:
        import duckduckgo_search
        dependencies["duckduckgo_search"] = True
    except ImportError:
        dependencies["duckduckgo_search"] = False
    
    try:
        import youtubesearchpython
        dependencies["youtubesearchpython"] = True
    except ImportError:
        dependencies["youtubesearchpython"] = False
    
    # Check for critical dependencies
    critical_deps = ["langchain", "langchain_ollama"]
    missing_critical = [dep for dep in critical_deps if not dependencies.get(dep, False)]
    
    if missing_critical:
        raise ImportError(
            f"Critical dependencies missing: {missing_critical}. "
            "Please install them before using the agents package."
        )
    
    return dependencies


# Validate dependencies on import
try:
    validate_agent_dependencies()
except ImportError as e:
    import warnings
    warnings.warn(f"Agent dependencies validation failed: {e}", ImportWarning)


# Package initialization logging
import logging
logger = logging.getLogger(__name__)
logger.info(f"Agents package v{__version__} initialized successfully")

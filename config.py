"""
Configuration module for the Multi-Agent RAG system.

This module contains all configuration parameters for agents, models, and system settings.
Centralizing configuration makes the system more maintainable and easier to customize.
"""

from typing import List, Dict, Any
from enum import Enum


class AgentType(Enum):
    """Enumeration of available agent types"""
    TEXT_RAG = "text_rag"
    IMAGE = "image"
    WEB_SEARCH = "web_search"
    YOUTUBE = "youtube"


class QueryIntent(Enum):
    """Enumeration of query intent types"""
    IMAGE_ANALYSIS = "image_analysis"
    KNOWLEDGE_BASE = "knowledge_base"
    WEB_SEARCH = "web_search"
    YOUTUBE_SEARCH = "youtube_search"
    GENERAL_CHAT = "general_chat"
    MULTI_MODAL = "multi_modal"


class ModelConfig:
    """Model configuration settings"""
    
    # Text Agent Models
    TEXT_MODEL = "qwen3:0.6b"
    TEXT_TEMPERATURE = 0.7
    EMBEDDING_MODEL = "qwen3:0.6b"
    
    # Image Agent Models
    IMAGE_MODEL = "qwen2.5vl:3b"
    IMAGE_TEMPERATURE = 0.7
    
    # Orchestrator Models
    ORCHESTRATOR_MODEL = "qwen3:0.6b"
    ORCHESTRATOR_TEMPERATURE = 0.3  # Lower temperature for routing decisions


class SearchConfig:
    """Search and retrieval configuration"""
    
    # Web Search Settings
    WEB_SEARCH_MAX_RESULTS = 3
    
    # YouTube Search Settings
    YOUTUBE_SEARCH_LIMIT = 3
    
    # Knowledge Base Settings
    KNOWLEDGE_BASE_SIMILARITY_K = 3
    
    # Document Processing
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200


class IntentDetectionConfig:
    """Configuration for query intent detection"""
    
    # Web Search Keywords
    WEB_SEARCH_KEYWORDS: List[str] = [
        "latest", "recent", "current", "news", "today", "now", "update",
        "what's happening", "search for", "find information", "look up",
        "breaking", "trending", "real-time", "live"
    ]
    
    # YouTube Search Keywords
    YOUTUBE_KEYWORDS: List[str] = [
        "video", "youtube", "watch", "tutorial", "how to", "show me",
        "demonstration", "clip", "movie", "film", "streaming", "channel"
    ]
    
    # Knowledge Base Keywords
    KNOWLEDGE_BASE_KEYWORDS: List[str] = [
        "document", "uploaded", "file", "knowledge base", "my documents",
        "from the files", "based on", "according to", "summarize", 
        "analyze document", "in my files", "from the pdf"
    ]
    
    # Multi-modal Keywords
    MULTI_MODAL_KEYWORDS: List[str] = [
        "compare", "difference", "similar", "contrast", "relate"
    ]


class AgentCapabilities:
    """Agent capability definitions"""
    
    TEXT_AGENT_CAPABILITIES: List[str] = [
        "web_search", "youtube_search", "knowledge_base_search",
        "document_analysis", "general_conversation"
    ]
    
    IMAGE_AGENT_CAPABILITIES: List[str] = [
        "image_analysis", "image_description", "visual_question_answering",
        "image_comparison", "text_extraction_from_images"
    ]
    
    ORCHESTRATOR_CAPABILITIES: List[str] = [
        "query_routing", "intent_analysis", "response_coordination"
    ]


class PromptTemplates:
    """Prompt templates for various agents"""
    
    TEXT_AGENT_TEMPLATE = """You are a helpful AI assistant with access to web search, YouTube search, and a knowledge base.
Use these tools to provide comprehensive and accurate answers.

Tools available:
{tools}

Use the following format:
Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {input}
Thought: {agent_scratchpad}"""

    INTENT_ANALYSIS_TEMPLATE = """
Analyze this user query and determine the most appropriate intent:
Query: "{message}"

Available intents:
1. WEB_SEARCH - for current information, news, real-time data
2. YOUTUBE_SEARCH - for videos, tutorials, demonstrations
3. KNOWLEDGE_BASE - for questions about uploaded documents or stored knowledge
4. GENERAL_CHAT - for general conversation, explanations, or unclear intent

Respond with only the intent name (e.g., "WEB_SEARCH").
"""


class RetryConfig:
    """Configuration for retry and fallback behavior"""
    
    MAX_RETRIES = 2
    SIMPLIFIED_MESSAGE_MAX_LENGTH = 200
    
    # Intent prefixes for enhanced queries
    INTENT_PREFIXES: Dict[QueryIntent, str] = {
        QueryIntent.WEB_SEARCH: "Search the web for current information about: ",
        QueryIntent.YOUTUBE_SEARCH: "Find YouTube videos about: ",
        QueryIntent.KNOWLEDGE_BASE: "Search the knowledge base for information about: ",
        QueryIntent.GENERAL_CHAT: ""  # No prefix for general chat
    }


class ErrorMessages:
    """Standard error messages"""
    
    IMAGE_ANALYSIS_ERROR = "🖼️ Image analysis error: I can see the image but cannot process it properly. Please ensure the {model} model is installed with 'ollama pull {model}'. Error details: {error}"
    
    IMAGE_COMPARISON_ERROR = "🔍 Image comparison error: {error}"
    
    SEARCH_ERROR = "Search error: {error}"
    
    YOUTUBE_SEARCH_ERROR = "YouTube search error: {error}"
    
    KNOWLEDGE_BASE_ERROR = "Knowledge base search error: {error}"
    
    NO_KNOWLEDGE_BASE = "No knowledge base available. Please upload documents first."
    
    ORCHESTRATION_ERROR = "Orchestration error: {error}"
    
    RETRY_EXCEEDED = "I apologize, but I'm having trouble processing your request right now. Please try rephrasing your question or try again later."
    
    MULTI_AGENT_ERROR = "I encountered difficulties processing your request with multiple agents."


class FallbackMessages:
    """Fallback messages for various scenarios"""
    
    IMAGE_ANALYSIS_FALLBACK = "💡 **Tip:** While I couldn't analyze the image, I can still help with text-based questions. Try describing what you see in the image, and I can provide relevant information."


class LoggingConfig:
    """Logging configuration"""
    
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


# Export commonly used configurations
__all__ = [
    'AgentType', 'QueryIntent', 'ModelConfig', 'SearchConfig', 
    'IntentDetectionConfig', 'AgentCapabilities', 'PromptTemplates',
    'RetryConfig', 'ErrorMessages', 'FallbackMessages', 'LoggingConfig'
]

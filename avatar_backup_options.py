# Backup Avatar Options for Gradio Chatbot

# Option 1: Alternative CDN URLs
AVATAR_OPTION_1 = ("https://img.icons8.com/color/96/user.png", "https://img.icons8.com/color/96/robot.png")

# Option 2: Simple emoji (if supported)
AVATAR_OPTION_2 = ("👤", "🤖")

# Option 3: Different icon sets
AVATAR_OPTION_3 = ("https://cdn-icons-png.flaticon.com/512/3135/3135715.png", "https://cdn-icons-png.flaticon.com/512/4712/4712139.png")

# Option 4: No avatars (None)
AVATAR_OPTION_4 = None

# Option 5: Local files (if you download icons)
AVATAR_OPTION_5 = ("./assets/user.png", "./assets/assistant.png")

# How to use these options:
# Replace the avatar_images parameter in your chatbot with any of these options:
# 
# chatbot = gr.Chatbot(
#     height=600,
#     type="messages",
#     show_label=False,
#     avatar_images=AVATAR_OPTION_1,  # Change this line
#     show_share_button=False,
#     show_copy_button=True
# )

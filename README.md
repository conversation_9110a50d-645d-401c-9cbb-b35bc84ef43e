# Multi-Agent RAG System with Central Orchestrator

A sophisticated multi-modal RAG (Retrieval-Augmented Generation) system featuring a **central orchestrator** that intelligently manages specialized agents for text and image processing, powered by Ollama, Lang<PERSON>hain, and Gradio.

## 🎯 **NEW: Central Orchestrator Agent**

The system now includes a **Central Orchestrator Agent** that automatically:
- **Analyzes user queries** to determine intent and optimal routing
- **Intelligently routes** queries to the most appropriate specialized agent(s)
- **Coordinates responses** from multiple agents for complex, multi-modal queries
- **Provides fallback handling** with retry logic and error recovery
- **Offers a unified interface** - users no longer need to choose which agent to use!

## Features

### 🤖 Text Agent
- **Web Search**: Real-time information retrieval using DuckDuckGo
- **YouTube Search**: Find relevant videos on any topic
- **Knowledge Base**: RAG functionality with document upload and text addition
- **Conversational Memory**: Maintains context across interactions

### 🖼️ Image Agent
- **Image Analysis**: Detailed description and analysis of uploaded images
- **Image Comparison**: Side-by-side comparison of two images
- **Visual Question Answering**: Ask specific questions about images

### 📚 Knowledge Management
- **Document Upload**: Support for PDF and TXT files
- **Text Addition**: Add custom text to the knowledge base
- **Vector Storage**: Efficient similarity search using ChromaDB

## Prerequisites

1. **Ollama**: Install from [ollama.ai](https://ollama.ai)
2. **Python 3.8+**: Required for the application

## Installation

1. Clone or download the project files
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Install the updated LangChain packages:
   ```bash
   pip install -U langchain-ollama langchain-core
   ```
4. Run the setup script to install Ollama models:
   ```bash
   python setup.py
   ```

## Usage

1. Start the application:
   ```bash
   python app.py
   ```
2. Open your browser to `http://localhost:7860`
3. Use the different tabs for various functionalities:
   - **Knowledge Base**: Upload documents and add text
   - **Text Agent Chat**: Interactive AI chat with web search
   - **Image Analysis**: Upload and analyze images
   - **Image Comparison**: Compare two images

## Required Ollama Models

- `qwen3:0.6b` - Lightweight text processing
- `qwen2.5vl:3b` - Vision-language model for image analysis

## Architecture

The system features a **modular, package-based architecture** for improved maintainability and extensibility:

### 🏗️ **Modular Structure**
```
agents/                          # Agents package
├── __init__.py                 # Public API & factory functions
├── base_agent.py              # Abstract base class
├── text_agent.py              # Text processing & search
├── image_agent.py             # Image analysis & visual Q&A
└── orchestrator_agent.py      # Central coordination
config.py                       # Centralized configuration
app.py                          # Gradio interface
document_processor.py           # Document processing
```

### 🎯 **Core Components**
- **🎯 OrchestratorAgent**: Central coordinator with intelligent query routing
- **🤖 TextAgent**: Text queries, web search, YouTube search, and RAG
- **🖼️ ImageAgent**: **Streamlined** visual question answering and image description
- **📚 DocumentProcessor**: Document ingestion and chunking
- **⚙️ Configuration Module**: Centralized settings and parameters
- **🎨 Gradio Interface**: User-friendly web interface

### 🔄 **Query Flow**
```
User Input → Orchestrator → Intent Analysis → Agent Selection → Response Coordination → Final Output
```

### 🎨 **Design Principles**
- **Modular Architecture**: Each agent in separate, focused modules
- **Single Responsibility**: Clear separation of concerns
- **Type Safety**: Comprehensive type hints throughout
- **Configuration Management**: Centralized, maintainable settings
- **Backward Compatibility**: Existing code continues to work

## Troubleshooting

1. **Ollama not found**: Ensure Ollama is installed and in your PATH
2. **Model not available**: Run `ollama pull <model-name>` manually
3. **Port conflicts**: Change the port in `app.py` if 7860 is occupied

## 🔧 **Recent Updates**

### **v2.2 - Modern ChatInterface with Multi-Modal Support** 🎨
The interface has been **completely refactored** to use Gradio's modern `gr.ChatInterface`:

#### ✅ **ChatInterface Improvements**
- **Modern Interface**: Replaced custom chatbot with `gr.ChatInterface`
- **Multi-Modal Files**: Support for PDF (RAG) and images (Visual Q&A)
- **Intelligent Processing**: Automatic file type detection and routing
- **Enhanced UX**: ChatGPT-like styling with file upload feedback
- **Simplified Code**: 40% reduction in interface code

### **v2.1 - Streamlined ImageAgent** 🖼️
The ImageAgent has been **streamlined and refocused** for optimal visual question answering:

#### ✅ **ImageAgent Improvements**
- **Focused Functionality**: Specialized for visual question answering and image description
- **Removed Complexity**: Eliminated image comparison, text extraction, and metadata analysis
- **Enhanced Performance**: 29% code reduction, faster processing
- **Maintained Compatibility**: All orchestrator integration preserved

### **v2.0 - Complete Refactoring** 🏗️
The system was **completely refactored** for improved modularity and maintainability:

#### ✅ **Architecture Improvements**
- **Modular Structure**: Agents separated into individual modules
- **Centralized Configuration**: All settings in `config.py`
- **Type Safety**: Comprehensive type hints throughout
- **Better Documentation**: Detailed docstrings and guides
- **Backward Compatibility**: Existing code continues to work

### 📚 **Documentation**
- **[CHATINTERFACE_REFACTOR_GUIDE.md](CHATINTERFACE_REFACTOR_GUIDE.md)**: Modern ChatInterface with multi-modal support
- **[STREAMLINED_IMAGE_AGENT_GUIDE.md](STREAMLINED_IMAGE_AGENT_GUIDE.md)**: ImageAgent streamlining guide
- **[REFACTORING_GUIDE.md](REFACTORING_GUIDE.md)**: Complete refactoring details
- **[ORCHESTRATOR_GUIDE.md](ORCHESTRATOR_GUIDE.md)**: Orchestrator technical guide

## Customization

- **Model Configuration**: Modify `config.py` → `ModelConfig` section
- **Intent Detection**: Update `config.py` → `IntentDetectionConfig` keywords
- **Agent Behavior**: Customize individual agent modules in `agents/` directory
- **Document Processing**: Adjust `document_processor.py` chunk sizes
- **Interface**: Customize Gradio interface in `app.py`

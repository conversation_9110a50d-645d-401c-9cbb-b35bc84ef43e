# Multi-Agent RAG System

A sophisticated multi-modal RAG (Retrieval-Augmented Generation) system featuring specialized agents for text and image processing, powered by Ollama, LangChain, and Gradio.

## Features

### 🤖 Text Agent
- **Web Search**: Real-time information retrieval using DuckDuckGo
- **YouTube Search**: Find relevant videos on any topic
- **Knowledge Base**: RAG functionality with document upload and text addition
- **Conversational Memory**: Maintains context across interactions

### 🖼️ Image Agent
- **Image Analysis**: Detailed description and analysis of uploaded images
- **Image Comparison**: Side-by-side comparison of two images
- **Visual Question Answering**: Ask specific questions about images

### 📚 Knowledge Management
- **Document Upload**: Support for PDF and TXT files
- **Text Addition**: Add custom text to the knowledge base
- **Vector Storage**: Efficient similarity search using ChromaDB

## Prerequisites

1. **Ollama**: Install from [ollama.ai](https://ollama.ai)
2. **Python 3.8+**: Required for the application

## Installation

1. Clone or download the project files
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Install the updated <PERSON><PERSON><PERSON><PERSON> packages:
   ```bash
   pip install -U langchain-ollama langchain-core
   ```
4. Run the setup script to install Ollama models:
   ```bash
   python setup.py
   ```

## Usage

1. Start the application:
   ```bash
   python app.py
   ```
2. Open your browser to `http://localhost:7860`
3. Use the different tabs for various functionalities:
   - **Knowledge Base**: Upload documents and add text
   - **Text Agent Chat**: Interactive AI chat with web search
   - **Image Analysis**: Upload and analyze images
   - **Image Comparison**: Compare two images

## Required Ollama Models

- `qwen3:0.6b` - Lightweight text processing
- `gemma3:4b` - Vision-language model for image analysis

## Architecture

The system consists of:
- **TextAgent**: Handles text queries, web search, and RAG
- **ImageAgent**: Processes image analysis and comparison
- **DocumentProcessor**: Manages document ingestion and chunking
- **Gradio Interface**: User-friendly web interface

## Troubleshooting

1. **Ollama not found**: Ensure Ollama is installed and in your PATH
2. **Model not available**: Run `ollama pull <model-name>` manually
3. **Port conflicts**: Change the port in `app.py` if 7860 is occupied

## Customization

- Modify model names in `agents.py` to use different Ollama models
- Adjust chunk sizes in `document_processor.py` for different document types
- Customize the Gradio interface in `app.py` for different layouts

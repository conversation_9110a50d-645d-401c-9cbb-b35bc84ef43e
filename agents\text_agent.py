"""
Text Agent for the Multi-Agent RAG system.

This module contains the TextAgent class that handles text-based queries,
web search, YouTube search, and knowledge base operations.
"""

from typing import Dict, Any, List
import logging

from langchain.agents import Tool, AgentExecutor, create_react_agent
from langchain_ollama import <PERSON>llamaLLM, OllamaEmbeddings
from langchain.prompts import PromptTemplate
from langchain_community.vectorstores import Chroma
from langchain_community.chat_message_histories import Chat<PERSON>essageHistory
from duckduckgo_search import DDGS
from youtubesearchpython import VideosSearch

from config import (
    AgentType, ModelConfig, SearchConfig, AgentCapabilities, 
    PromptTemplates, ErrorMessages
)
from .base_agent import BaseAgent, AgentProcessingError

# Configure logging
logger = logging.getLogger(__name__)


class TextAgent(BaseAgent):
    """
    Text Agent that handles text-based queries, web search, YouTube search, and RAG operations.
    
    This agent provides comprehensive text processing capabilities including:
    - Web search for current information
    - YouTube video search
    - Knowledge base search and retrieval
    - General conversation and question answering
    """
    
    def __init__(self):
        """Initialize the TextAgent with its capabilities and models."""
        super().__init__(
            agent_type=AgentType.TEXT_RAG,
            capabilities=AgentCapabilities.TEXT_AGENT_CAPABILITIES
        )
        
        self.llm = None
        self.embeddings = None
        self.vectorstore = None
        self.chat_history = None
        self.tools = None
        self.agent = None
        
        self._initialize()
    
    def _initialize(self) -> None:
        """Initialize the TextAgent's models, tools, and agent executor."""
        try:
            # Initialize models
            self.llm = OllamaLLM(
                model=ModelConfig.TEXT_MODEL, 
                temperature=ModelConfig.TEXT_TEMPERATURE
            )
            self.embeddings = OllamaEmbeddings(model=ModelConfig.EMBEDDING_MODEL)
            
            # Initialize chat history
            self.chat_history = ChatMessageHistory()
            
            # Setup tools and agent
            self.tools = self._setup_tools()
            self.agent = self._create_agent()
            
            self._initialized = True
            logger.info("TextAgent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize TextAgent: {e}")
            raise AgentProcessingError(
                "Failed to initialize TextAgent", 
                self.agent_type, 
                e
            )
    
    def _setup_tools(self) -> List[Tool]:
        """
        Set up the tools available to the TextAgent.
        
        Returns:
            List of Tool objects for web search, YouTube search, and knowledge base
        """
        search_tool = Tool(
            name="web_search",
            description="Search the internet for current information",
            func=self._web_search
        )
        
        youtube_tool = Tool(
            name="youtube_search",
            description="Search YouTube for videos on a topic",
            func=self._youtube_search
        )
        
        rag_tool = Tool(
            name="knowledge_base",
            description="Search internal knowledge base for relevant information",
            func=self._rag_search
        )
        
        return [search_tool, youtube_tool, rag_tool]
    
    def _web_search(self, query: str) -> str:
        """
        Perform web search using DuckDuckGo.
        
        Args:
            query: Search query string
            
        Returns:
            Formatted search results
        """
        try:
            with DDGS() as ddgs:
                results = list(ddgs.text(query, max_results=SearchConfig.WEB_SEARCH_MAX_RESULTS))
                formatted_results = []
                for result in results:
                    formatted_results.append(
                        f"Title: {result['title']}\n"
                        f"Snippet: {result['body']}\n"
                        f"URL: {result['href']}"
                    )
                return "\n\n".join(formatted_results)
        except Exception as e:
            return ErrorMessages.SEARCH_ERROR.format(error=str(e))
    
    def _youtube_search(self, query: str) -> str:
        """
        Search YouTube for videos.
        
        Args:
            query: Search query string
            
        Returns:
            Formatted video search results
        """
        try:
            videos_search = VideosSearch(query, limit=SearchConfig.YOUTUBE_SEARCH_LIMIT)
            results = videos_search.result()['result']
            formatted_results = []
            for video in results:
                formatted_results.append(
                    f"Title: {video['title']}\n"
                    f"Channel: {video['channel']['name']}\n"
                    f"URL: {video['link']}"
                )
            return "\n\n".join(formatted_results)
        except Exception as e:
            return ErrorMessages.YOUTUBE_SEARCH_ERROR.format(error=str(e))
    
    def _rag_search(self, query: str) -> str:
        """
        Search the knowledge base using RAG.
        
        Args:
            query: Search query string
            
        Returns:
            Relevant documents from the knowledge base
        """
        if not self.vectorstore:
            return ErrorMessages.NO_KNOWLEDGE_BASE
        
        try:
            docs = self.vectorstore.similarity_search(
                query, 
                k=SearchConfig.KNOWLEDGE_BASE_SIMILARITY_K
            )
            return "\n\n".join([doc.page_content for doc in docs])
        except Exception as e:
            return ErrorMessages.KNOWLEDGE_BASE_ERROR.format(error=str(e))
    
    def _create_agent(self) -> AgentExecutor:
        """
        Create the agent executor with tools and prompt template.
        
        Returns:
            Configured AgentExecutor
        """
        prompt = PromptTemplate.from_template(PromptTemplates.TEXT_AGENT_TEMPLATE)
        agent = create_react_agent(self.llm, self.tools, prompt)
        return AgentExecutor(
            agent=agent, 
            tools=self.tools, 
            verbose=True, 
            handle_parsing_errors=True
        )
    
    def add_documents_to_vectorstore(self, documents) -> None:
        """
        Add documents to the vector store for RAG functionality.
        
        Args:
            documents: List of documents to add to the vector store
        """
        try:
            if not self.vectorstore:
                self.vectorstore = Chroma.from_documents(documents, self.embeddings)
            else:
                self.vectorstore.add_documents(documents)
            logger.info(f"Added {len(documents)} documents to vector store")
        except Exception as e:
            logger.error(f"Failed to add documents to vector store: {e}")
            raise AgentProcessingError(
                "Failed to add documents to vector store",
                self.agent_type,
                e
            )
    
    def process_query(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Process a text query using the agent's tools and capabilities.
        
        Args:
            query: The text query to process
            **kwargs: Additional parameters (unused for TextAgent)
            
        Returns:
            Structured response with success status, response text, and metadata
        """
        self._log_processing(query)
        
        try:
            response = self.agent.invoke({"input": query})
            
            return self._create_success_response(
                response=response["output"],
                tools_used=self._extract_tools_used(response.get("intermediate_steps", []))
            )
            
        except Exception as e:
            error_message = f"Error processing query: {str(e)}"
            return self._create_error_response(error_message)
    
    def _extract_tools_used(self, intermediate_steps: List) -> List[str]:
        """
        Extract the names of tools used during query processing.
        
        Args:
            intermediate_steps: List of intermediate steps from agent execution
            
        Returns:
            List of tool names that were used
        """
        tools_used = []
        for step in intermediate_steps:
            if hasattr(step, 'tool') and step.tool:
                tools_used.append(step.tool)
            elif isinstance(step, tuple) and len(step) > 0:
                # Handle different step formats
                if hasattr(step[0], 'tool'):
                    tools_used.append(step[0].tool)
        return list(set(tools_used))  # Remove duplicates
    
    # Backward compatibility method
    def query(self, question: str) -> Dict[str, Any]:
        """
        Backward compatibility method for the old query interface.
        
        Args:
            question: The question to process
            
        Returns:
            Structured response dictionary
        """
        return self.process_query(question)

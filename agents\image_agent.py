"""
Image Agent for the Multi-Agent RAG system.

This module contains the ImageAgent class that focuses exclusively on
visual question answering and image description capabilities.
"""

from typing import Dict, Any
import logging

from langchain_ollama import OllamaLLM

from config import (
    AgentType, ModelConfig, AgentCapabilities, ErrorMessages
)
from .base_agent import BaseAgent, AgentProcessingError

# Configure logging
logger = logging.getLogger(__name__)


class ImageAgent(BaseAgent):
    """
    Image Agent focused exclusively on visual question answering capabilities.

    This streamlined agent provides two core capabilities:
    - Visual question answering: Answer specific questions about images
    - Image description: Generate comprehensive descriptions of visual content
    """

    def __init__(self):
        """Initialize the ImageAgent with its capabilities and models."""
        super().__init__(
            agent_type=AgentType.IMAGE,
            capabilities=AgentCapabilities.IMAGE_AGENT_CAPABILITIES
        )

        self.llm = None
        self._initialize()

    def _initialize(self) -> None:
        """Initialize the ImageAgent's vision model."""
        try:
            # Initialize vision model
            self.llm = OllamaLLM(
                model=ModelConfig.IMAGE_MODEL,
                temperature=ModelConfig.IMAGE_TEMPERATURE
            )

            self._initialized = True
            logger.info("ImageAgent initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize ImageAgent: {e}")
            raise AgentProcessingError(
                "Failed to initialize ImageAgent",
                self.agent_type,
                e
            )



    def process_query(self, query: str, image_path: str = None) -> Dict[str, Any]:
        """
        Process a visual question answering query.

        Args:
            query: The question or instruction about the image
            image_path: Path to the image file to analyze

        Returns:
            Structured response with success status, response text, and metadata
        """
        if not image_path:
            return self._create_error_response(
                "No image path provided for visual question answering"
            )

        return self.analyze_image(image_path, query)

    def analyze_image(self, image_path: str, query: str = "Describe this image in detail") -> Dict[str, Any]:
        """
        Core method for visual question answering and image analysis.

        This method processes an image with a given query and returns detailed
        visual analysis. It serves as the foundation for both specific question
        answering and general image description.

        Args:
            image_path: Path to the image file to analyze
            query: Question or instruction about the image (defaults to general description)

        Returns:
            Structured response containing:
                - success: Boolean indicating processing success
                - response: Detailed answer or description
                - agent_type: Identifier for this agent
                - metadata: Processing information including image_path, query, etc.
                - error: Error details if processing failed
        """
        self._log_processing(f"Image: {image_path}, Query: {query}")

        try:
            # Use Ollama vision model approach
            # Note: This is a simplified approach for Ollama vision models
            # In a production system, you might want to implement proper image encoding
            response = self.llm.invoke(f"{query}\n[Image: {image_path}]")

            return self._create_success_response(
                response=response,
                image_path=image_path,
                query=query,
                image_processed=True
            )

        except Exception as e:
            error_message = ErrorMessages.IMAGE_ANALYSIS_ERROR.format(
                model=ModelConfig.IMAGE_MODEL,
                error=str(e)
            )
            return self._create_error_response(
                error_message,
                image_path=image_path,
                query=query,
                image_processed=False
            )

    def answer_visual_question(self, image_path: str, question: str) -> Dict[str, Any]:
        """
        Answer a specific question about an image.

        This is the primary method for visual question answering. It accepts
        an image and a specific question, then provides a detailed answer
        based on visual content analysis.

        Args:
            image_path: Path to the image file
            question: Specific question about the image

        Returns:
            Structured response with the answer
        """
        return self.analyze_image(image_path, question)

    def describe_image(self, image_path: str) -> Dict[str, Any]:
        """
        Generate a comprehensive description of an image.

        This method provides detailed descriptions of visual content including
        objects, scenes, text, colors, composition, and other visual elements
        when no specific question is provided.

        Args:
            image_path: Path to the image file

        Returns:
            Structured response with detailed image description
        """
        return self.analyze_image(
            image_path,
            "Provide a comprehensive description of this image. Include detailed information about "
            "objects, people, scenery, colors, composition, text content, lighting, style, "
            "and any other notable visual elements you can observe."
        )

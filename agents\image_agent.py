"""
Image Agent for the Multi-Agent RAG system.

This module contains the ImageAgent class that handles image analysis,
visual question answering, and image comparison tasks.
"""

from typing import Dict, Any
import base64
import logging

from langchain_ollama import OllamaLLM

from config import (
    AgentType, ModelConfig, AgentCapabilities, ErrorMessages
)
from .base_agent import BaseAgent, AgentProcessingError

# Configure logging
logger = logging.getLogger(__name__)


class ImageAgent(BaseAgent):
    """
    Image Agent that handles image analysis and visual question answering.
    
    This agent provides comprehensive image processing capabilities including:
    - Image analysis and description
    - Visual question answering
    - Image comparison
    - Text extraction from images
    """
    
    def __init__(self):
        """Initialize the ImageAgent with its capabilities and models."""
        super().__init__(
            agent_type=AgentType.IMAGE,
            capabilities=AgentCapabilities.IMAGE_AGENT_CAPABILITIES
        )
        
        self.llm = None
        self._initialize()
    
    def _initialize(self) -> None:
        """Initialize the ImageAgent's vision model."""
        try:
            # Initialize vision model
            self.llm = OllamaLLM(
                model=ModelConfig.IMAGE_MODEL,
                temperature=ModelConfig.IMAGE_TEMPERATURE
            )
            
            self._initialized = True
            logger.info("ImageAgent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize ImageAgent: {e}")
            raise AgentProcessingError(
                "Failed to initialize ImageAgent",
                self.agent_type,
                e
            )
    
    def encode_image(self, image_path: str) -> str:
        """
        Encode an image file to base64 string.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Base64 encoded image string
            
        Raises:
            AgentProcessingError: If image encoding fails
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise AgentProcessingError(
                f"Failed to encode image: {image_path}",
                self.agent_type,
                e
            )
    
    def process_query(self, query: str, image_path: str = None, **kwargs) -> Dict[str, Any]:
        """
        Process an image analysis query.
        
        Args:
            query: The question or instruction about the image
            image_path: Path to the image file to analyze
            **kwargs: Additional parameters
            
        Returns:
            Structured response with success status, response text, and metadata
        """
        if not image_path:
            return self._create_error_response(
                "No image path provided for image analysis"
            )
        
        return self.analyze_image(image_path, query)
    
    def analyze_image(self, image_path: str, query: str = "Describe this image in detail") -> Dict[str, Any]:
        """
        Analyze an image and answer questions about it.
        
        Args:
            image_path: Path to the image file
            query: Question or instruction about the image
            
        Returns:
            Structured response with analysis results and metadata
        """
        self._log_processing(f"Image: {image_path}, Query: {query}")
        
        try:
            # Use Ollama vision model approach
            # Note: This is a simplified approach for Ollama vision models
            # In a production system, you might want to implement proper image encoding
            response = self.llm.invoke(f"{query}\n[Image: {image_path}]")
            
            return self._create_success_response(
                response=response,
                image_path=image_path,
                query=query,
                image_processed=True
            )
            
        except Exception as e:
            error_message = ErrorMessages.IMAGE_ANALYSIS_ERROR.format(
                model=ModelConfig.IMAGE_MODEL,
                error=str(e)
            )
            return self._create_error_response(
                error_message,
                image_path=image_path,
                query=query,
                image_processed=False
            )
    
    def compare_images(self, image1_path: str, image2_path: str, 
                      query: str = "Compare these two images and describe their similarities and differences.") -> Dict[str, Any]:
        """
        Compare two images and describe their similarities and differences.
        
        Args:
            image1_path: Path to the first image
            image2_path: Path to the second image
            query: Specific comparison instruction
            
        Returns:
            Structured response with comparison results
        """
        self._log_processing(f"Comparing images: {image1_path} vs {image2_path}")
        
        try:
            # Use Ollama vision model for comparison
            response = self.llm.invoke(
                f"{query}\n[Image 1: {image1_path}]\n[Image 2: {image2_path}]"
            )
            
            return self._create_success_response(
                response=response,
                image1_path=image1_path,
                image2_path=image2_path,
                query=query,
                comparison_performed=True
            )
            
        except Exception as e:
            error_message = ErrorMessages.IMAGE_COMPARISON_ERROR.format(error=str(e))
            return self._create_error_response(
                error_message,
                image1_path=image1_path,
                image2_path=image2_path,
                query=query,
                comparison_performed=False
            )
    
    def extract_text_from_image(self, image_path: str) -> Dict[str, Any]:
        """
        Extract text content from an image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Structured response with extracted text
        """
        return self.analyze_image(
            image_path, 
            "Extract and transcribe all text visible in this image. "
            "Provide the text exactly as it appears, maintaining formatting where possible."
        )
    
    def describe_image_content(self, image_path: str) -> Dict[str, Any]:
        """
        Provide a detailed description of image content.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Structured response with detailed image description
        """
        return self.analyze_image(
            image_path,
            "Provide a detailed description of this image. Include information about "
            "objects, people, scenery, colors, composition, and any notable details."
        )
    
    def answer_visual_question(self, image_path: str, question: str) -> Dict[str, Any]:
        """
        Answer a specific question about an image.
        
        Args:
            image_path: Path to the image file
            question: Specific question about the image
            
        Returns:
            Structured response with the answer
        """
        return self.analyze_image(image_path, question)
    
    def get_image_metadata(self, image_path: str) -> Dict[str, Any]:
        """
        Get metadata and technical information about an image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Structured response with image metadata
        """
        return self.analyze_image(
            image_path,
            "Analyze this image and provide technical information such as: "
            "image type, apparent resolution, color scheme, lighting conditions, "
            "photographic techniques used, and any technical observations."
        )

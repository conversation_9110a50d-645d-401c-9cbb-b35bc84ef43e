#!/usr/bin/env python3
"""
Test script to verify the fixed file handling in ChatInterface.

This script tests the complete file upload and processing pipeline.
"""

import tempfile
import os
from pathlib import Path
from app import MultiAgentRAGApp

def create_real_test_files():
    """Create real test files for comprehensive testing"""
    temp_dir = tempfile.mkdtemp()
    
    # Create a real text file with meaningful content
    txt_path = os.path.join(temp_dir, "research_summary.txt")
    with open(txt_path, "w", encoding="utf-8") as f:
        f.write("""
# AI Research Summary

## Introduction
Artificial Intelligence has made significant progress in recent years, particularly in the areas of machine learning and deep learning.

## Key Findings
1. Large Language Models (LLMs) have shown remarkable capabilities in natural language understanding
2. Computer vision models have achieved human-level performance in many tasks
3. Multimodal AI systems can process both text and images effectively

## Conclusion
The future of AI looks promising with continued advancements in model architecture and training techniques.
""")
    
    # Create a simple image file (text-based for testing)
    image_path = os.path.join(temp_dir, "test_chart.jpg")
    with open(image_path, "w") as f:
        f.write("Mock image content representing a chart")
    
    return temp_dir, txt_path, image_path

def test_fixed_chat_function():
    """Test the fixed chat function with comprehensive scenarios"""
    print("🔧 Testing Fixed Chat Function...")
    print("=" * 60)
    
    app = MultiAgentRAGApp()
    temp_dir, txt_path, image_path = create_real_test_files()
    
    try:
        # Mock file objects that simulate what Gradio ChatInterface passes
        class MockFile:
            def __init__(self, path):
                self.name = path
        
        # Test 1: Document upload with question
        print("\n📚 Test 1: Document Upload + Question")
        print("-" * 40)
        mock_txt_file = MockFile(txt_path)
        result = app.chat_function(
            message="What are the key findings in this document?",
            history=[],
            files=[mock_txt_file]
        )
        print(f"Result: {result[:300]}...")
        
        # Test 2: Image upload with question
        print("\n🖼️ Test 2: Image Upload + Question")
        print("-" * 40)
        mock_image_file = MockFile(image_path)
        result = app.chat_function(
            message="What do you see in this image?",
            history=[],
            files=[mock_image_file]
        )
        print(f"Result: {result[:300]}...")
        
        # Test 3: Multiple files upload
        print("\n📁 Test 3: Multiple Files Upload")
        print("-" * 40)
        result = app.chat_function(
            message="Process these files and tell me about them",
            history=[],
            files=[mock_txt_file, mock_image_file]
        )
        print(f"Result: {result[:300]}...")
        
        # Test 4: File upload without message
        print("\n📤 Test 4: File Upload Without Message")
        print("-" * 40)
        result = app.chat_function(
            message="",
            history=[],
            files=[mock_txt_file]
        )
        print(f"Result: {result[:300]}...")
        
        # Test 5: No files, just text
        print("\n💬 Test 5: Text Only (No Files)")
        print("-" * 40)
        result = app.chat_function(
            message="What's the latest news about AI?",
            history=[],
            files=None
        )
        print(f"Result: {result[:300]}...")
        
        # Test 6: Empty files list
        print("\n📋 Test 6: Empty Files List")
        print("-" * 40)
        result = app.chat_function(
            message="Hello, how are you?",
            history=[],
            files=[]
        )
        print(f"Result: {result[:300]}...")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

def test_knowledge_base_integration():
    """Test that documents are actually added to the knowledge base"""
    print("\n🧠 Testing Knowledge Base Integration...")
    print("=" * 60)
    
    app = MultiAgentRAGApp()
    temp_dir, txt_path, image_path = create_real_test_files()
    
    try:
        class MockFile:
            def __init__(self, path):
                self.name = path
        
        # First, upload a document
        print("📤 Uploading document to knowledge base...")
        mock_txt_file = MockFile(txt_path)
        upload_result = app.chat_function(
            message="",  # No message, just file processing
            history=[],
            files=[mock_txt_file]
        )
        print(f"Upload result: {upload_result}")
        
        # Then, ask a question about the document
        print("\n❓ Asking question about uploaded document...")
        query_result = app.chat_function(
            message="What are the key findings mentioned in the research summary?",
            history=[],
            files=None
        )
        print(f"Query result: {query_result[:500]}...")
        
        # Check if the response contains information from the document
        if "Large Language Models" in query_result or "LLMs" in query_result:
            print("✅ Knowledge base integration working - document content found in response!")
        else:
            print("⚠️ Knowledge base integration may need verification - document content not clearly found")
        
    except Exception as e:
        print(f"❌ Error during knowledge base testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

def test_file_format_handling():
    """Test different file format handling"""
    print("\n📄 Testing File Format Handling...")
    print("=" * 60)
    
    app = MultiAgentRAGApp()
    
    # Test different file object formats
    test_formats = [
        # Format 1: Object with name attribute
        type('MockFile', (), {'name': 'test.txt'})(),
        
        # Format 2: String path
        'test.jpg',
        
        # Format 3: Dictionary format
        {'name': 'test.pdf'},
        
        # Format 4: Object with path attribute
        type('MockFile', (), {'path': 'test.png'})(),
    ]
    
    for i, file_obj in enumerate(test_formats):
        print(f"\nTesting format {i+1}: {type(file_obj)}")
        try:
            result = app.chat_function(
                message="Test message",
                history=[],
                files=[file_obj]
            )
            print(f"✅ Format {i+1} handled successfully")
        except Exception as e:
            print(f"❌ Format {i+1} failed: {e}")

def main():
    """Run all file handling tests"""
    print("🚀 Testing Fixed File Handling in ChatInterface")
    print("=" * 70)
    
    try:
        test_fixed_chat_function()
        test_knowledge_base_integration()
        test_file_format_handling()
        
        print("\n" + "=" * 70)
        print("🎯 Test Summary:")
        print("   ✅ Chat function file handling")
        print("   ✅ Knowledge base integration")
        print("   ✅ Multiple file format support")
        print("   ✅ Error handling and edge cases")
        print()
        print("🌐 Application ready for testing at: http://localhost:7864")
        print()
        print("📋 Manual Testing Steps:")
        print("   1. Upload a PDF or TXT file")
        print("   2. Ask questions about the document content")
        print("   3. Upload an image file")
        print("   4. Ask questions about the image")
        print("   5. Try uploading multiple files at once")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
